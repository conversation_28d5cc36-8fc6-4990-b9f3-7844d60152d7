import { BillingInterval, Currency, SubscriptionPlan } from '@prisma/client';
import { createZodDto } from 'nestjs-zod';
import { JsonValueSchema } from '../../_schemas';
import { z } from 'zod';

// ==================== ENUMS ====================
enum RegionsEnum {
  US = 'US',
  NP = 'NP',
}

// ==================== SCHEMAS ====================

// Base plan schema from database
const BasePlanSchema = z.object({
  id: z.string().uuid(),
  name: z.nativeEnum(SubscriptionPlan),
  description: z.string().nullable(),
  amount: z.number(),
  interval: z.nativeEnum(BillingInterval),
  durationInDays: z.number(),
  features: JsonValueSchema,
  isActive: z.boolean(),
  isPopular: z.boolean(),
});

// Processed plan schema with tax information
const ProcessedPlanSchema = BasePlanSchema.extend({
  currency: z.nativeEnum(Currency),
  taxRate: z.number().optional(),
  taxAmount: z.number().optional(),
  originalAmount: z.number().optional(),
});

// Array of processed plans
const ProcessedPlansArraySchema = z.array(ProcessedPlanSchema);

// Filter schema for querying plans
const PlansFilterSchema = z.object({
  region: z.nativeEnum(RegionsEnum).optional(),
  currency: z.nativeEnum(Currency).optional(),
  interval: z.nativeEnum(BillingInterval).optional(),
});

// Create plan schema
const CreatePlanSchema = z.object({
  name: z.string().min(1),
  description: z.string().nullable().optional(),
  features: JsonValueSchema.nullable(),
  isActive: z.boolean().default(true),
  isPopular: z.boolean().default(false),
  amount: z.number(),
  interval: z.nativeEnum(BillingInterval),
  durationInDays: z.number(),
});

// Update plan schema - allows partial updates
const UpdatePlanSchema = CreatePlanSchema.partial();

// ==================== TYPES ====================

// Infer types from schemas
export type BasePlan = z.infer<typeof BasePlanSchema>;
export type ProcessedPlan = z.infer<typeof ProcessedPlanSchema>;
export type PlansFilter = z.infer<typeof PlansFilterSchema>;
export type CreatePlan = z.infer<typeof CreatePlanSchema>;
export type UpdatePlan = z.infer<typeof UpdatePlanSchema>;

// ==================== DTOs ====================

// DTOs for controllers
export class PlansFilterDto extends createZodDto(PlansFilterSchema) {}
export class CreatePlanDto extends createZodDto(CreatePlanSchema) {}
export class UpdatePlanDto extends createZodDto(UpdatePlanSchema) {}
export class ProcessedPlanDto extends createZodDto(ProcessedPlanSchema) {}
export class ProcessedPlansDto extends createZodDto(ProcessedPlansArraySchema) {}
