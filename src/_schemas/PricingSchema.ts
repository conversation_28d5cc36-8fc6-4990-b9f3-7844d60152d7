import { z } from 'zod';
import { Prisma, Currency, BillingInterval } from '@prisma/client';

/////////////////////////////////////////
// PRICING SCHEMA
/////////////////////////////////////////

export const PricingSchema = z.object({
  currency: z.nativeEnum(Currency),
  interval: z.nativeEnum(BillingInterval),
  id: z.string().uuid(),
  planId: z.string(),
  amount: z.instanceof(Prisma.Decimal, { message: "Field 'amount' must be a Decimal. Location: ['Models', 'Pricing']" }),
  regionCode: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
});

export type Pricing = z.infer<typeof PricingSchema>;

export default PricingSchema;
