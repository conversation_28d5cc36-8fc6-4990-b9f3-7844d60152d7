import { UserStatus } from '@prisma/client';
import { z } from 'zod';

/////////////////////////////////////////
// USER SCHEMA
/////////////////////////////////////////

export const UserSchema = z.object({
  userStatus: z.nativeEnum(UserStatus),
  id: z.string().uuid(),
  email: z.string(),
  name: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  isVerified: z.boolean(),
  verificationToken: z.string().nullable(),
  verificationExpiresAt: z.coerce.date().nullable(),
  roleId: z.string(),
});

export type User = z.infer<typeof UserSchema>;

export default UserSchema;
