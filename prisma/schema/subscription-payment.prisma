model Subscription {
  id        String             @id @default(uuid()) @db.Uuid
  tenantId  String             @unique @map("tenant_id") @db.Uuid
  planId    String             @map("plan_id") @db.Uuid
  status    SubscriptionStatus @map("status")
  startDate DateTime           @map("start_date")
  endDate   DateTime           @map("end_date")
  createdAt DateTime           @default(now()) @map("created_at")
  updatedAt DateTime           @updatedAt @map("updated_at")

  // Relations
  tenant    Tenant             @relation(fields: [tenantId], references: [id])
  plan      Plan               @relation(fields: [planId], references: [id])
  transactions Transaction[]

  @@index([status])
  @@index([planId])
  @@map("subscriptions")
}

model PaymentMethod {
  id             String         @id @default(uuid()) @db.Uuid
  paymentGateway PaymentGateway @map("payment_gateway")
  isPrimary      Boolean        @default(false) @map("is_primary")
  tenantId       String         @map("tenant_id") @db.Uuid
  createdAt      DateTime       @default(now()) @map("created_at")
  updatedAt      DateTime       @updatedAt @map("updated_at")

  // Relations
  transactions   Transaction[]
  tenant         Tenant         @relation(fields: [tenantId], references: [id])

  @@index([paymentGateway])
  @@map("payment_method")
}

model Transaction {
  id                 String            @id @default(uuid()) @db.Uuid
  paymentMethodId    String?           @map("payment_method_id") @db.Uuid
  subscriptionId     String?           @map("subscription_id") @db.Uuid
  planId             String            @map("plan_id") @db.Uuid
  amount             Decimal           @map("amount") @db.Decimal()
  currency           Currency          @default(NPR) @map("currency")
  status             TransactionStatus @map("status")
  transactionType    TransactionType   @default(CHARGE) @map("transaction_type")
  purchaseOrderId    String?           @map("purchase_order_id")
  purchaseOrderName  String?           @map("purchase_order_name")
  transactionDetails Json              @map("transaction_details")
  createdAt          DateTime          @default(now()) @map("created_at")
  updatedAt          DateTime          @updatedAt @map("updated_at")

  // Relations
  paymentMethod      PaymentMethod?    @relation(fields: [paymentMethodId], references: [id])
  subscription       Subscription?     @relation(fields: [subscriptionId], references: [id])
  plan               Plan              @relation(fields: [planId], references: [id])

  @@index([paymentMethodId])
  @@index([subscriptionId])
  @@index([status])
  @@index([transactionType])
  @@map("transactions")
}

model Plan {
  id              String           @id @default(uuid()) @db.Uuid
  name            SubscriptionPlan @unique
  description     String?
  amount          Int              // Amount in minor unit (e.g. paisa or cent )
  interval        BillingInterval
  durationInDays  Int              @map("duration_in_days")      
  features        Json?            // e.g. {"users": 10, "branding": true}
  isActive        Boolean          @default(true)
  isPopular       Boolean          @default(false)
  createdAt       DateTime         @default(now()) @map("created_at")
  updatedAt       DateTime         @updatedAt @map("updated_at")

  subscriptions   Subscription[]
  transactions    Transaction[]

  @@index([name])
  @@map("plan")
}

enum TransactionStatus {
  PENDING
  SUCCESS
  FAILED
  REFUNDED
  CANCELLED
  REVERSED
}

enum TransactionType {
  CHARGE
  REFUND
  TRIAL
  ADJUSTMENT
}

enum Currency {
  USD
  NPR
}

enum BillingInterval {
  MONTHLY
  YEARLY
  LIFETIME
}

enum SubscriptionStatus {
  ACTIVE
  EXPIRED
  CANCELLED
  TRIAL
}

enum SubscriptionPlan {
  FREE  
  BASIC
  PREMIUM
  PLATINUM
  BASIC_YEARLY
  PREMIUM_YEARLY
  PLATINUM_YEARLY
}

enum PaymentGateway {
  STRIPE
  ESEWA
  KHALTI
  FONEPAY
}
