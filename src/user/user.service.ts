import {
  BadRequestException,
  ConflictException,
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { UserCreateDto, UserResponseDto, UserResponseWithRoleDto } from './dto/user.dto';
import { Prisma, PrismaClient, SystemRoles, TenantInvitation, User } from '@prisma/client';
import { PasswordService } from 'src/password/password.service';
import { HashService } from 'src/_utils/hash.utils';

/**
 * Service responsible for user management operations
 * Handles user creation, retrieval, and tenant user management
 */
@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly passwordService: PasswordService,
  ) {}

  // Get user details with the roles using id.
  async getUserById(id: string): Promise<UserResponseWithRoleDto> {
    return this.findUserOrThrow({ id });
  }
  // get user details with the roles using email.
  async getUserByEmail(email: string): Promise<UserResponseWithRoleDto> {
    return this.findUserOrThrow({ email });
  }

  //get all the users for the super admin
  async getAllUsers(): Promise<UserResponseDto[]> {
    const users = await this.prismaService.user.findMany();
    if (!users.length) throw new NotFoundException('No users found');
    return users;
  }

  //delete user details with the roles using id.
  async deleteUser(id: string): Promise<void> {
    await this.prismaService.user.delete({ where: { id } });
  }

  //change password for the user using id when the user is logged in.
  async changePassword(id: string, newPassword: string, oldPassword: string): Promise<void> {
    // get the password record for the given user.
    const passwordRecord = await this.passwordService.findPasswordRecord(id);
    // check if the user exists and password is present
    if (!passwordRecord) throw new NotFoundException('User not found');

    //compare the input password and stored password record
    const isPasswordValid = HashService.compareHash({
      storedHash: passwordRecord.hash,
      storedSalt: passwordRecord.salt,
      tobeHashed: oldPassword,
    });

    // return  if the password is not valid
    if (!isPasswordValid) throw new BadRequestException('Invalid password Please try again');

    //update the password record with the new password
    await this.passwordService.createOrUpadatePassword(id, newPassword);
  }

  private async findUserOrThrow(criteria: { id?: string; email?: string }): Promise<UserResponseWithRoleDto> {
    // Determine which field to search by
    const searchKey = criteria.id ? 'id' : 'email';
    const searchValue = criteria.id || criteria.email;

    if (!searchValue) {
      throw new Error('Either id or email must be provided to find a user');
    }

    // Construct a proper Prisma where clause
    const where = searchKey === 'id' ? { id: criteria.id! } : { email: criteria.email! };

    const userWithRole = await this.prismaService.user.findUnique({ where, include: { Role: { select: { type: true } } } });

    const tenantUser = await this.prismaService.tenantUser.findFirst({ where: { userId: userWithRole?.id } });

    console.log(`tenant user`, tenantUser);
    if (!userWithRole) throw new NotFoundException(`The user record does not exist`);

    const user = {
      ...userWithRole,
      role: userWithRole?.Role.type,
    };
    return user;
  }

  async createTenantAdmin(payload: UserCreateDto): Promise<User> {
    try {
      console.log(payload);
      // Using transaction to ensure data consistency
      return this.prismaService.$transaction(async prisma => {
        // Find the tenant admin role
        const role = await prisma.role.findFirst({ where: { type: SystemRoles.TENANT_ADMIN } });
        if (!role) throw new NotFoundException(`${SystemRoles.TENANT_ADMIN} role not found`);

        // Check if user already exists
        await this.checkIfUserExistsAlready(payload.email);

        // Create user with password
        return this.createUserWithPassword(payload, role.id, prisma);
      });
    } catch (error) {
      this.handleUserCreationError(error, 'Failed to create tenant admin');
    }
  }

  async createUserWithInvitation(payload: UserCreateDto, token: string): Promise<User> {
    try {
      // Validate invitation
      const invitation = await this.getInvitationOrThrow(token, payload.email);

      // Using transaction to ensure data consistency
      return await this.prismaService.$transaction(async prisma => {
        // Create user with password
        const user = await this.createUserWithPassword(payload, invitation.roleId, prisma);

        console.log(invitation.tenantId);

        // Create tenant-user relationship
        await prisma.tenantUser.create({
          data: {
            userId: user.id,
            tenantId: invitation.tenantId,
            roleId: invitation.roleId,
          },
        });

        return user;
      });
    } catch (error) {
      this.handleUserCreationError(error, 'Failed to create tenant member');
    }
  }

  private async createUserWithPassword(
    payload: UserCreateDto,
    roleId: string,
    prisma: PrismaClient | Prisma.TransactionClient = this.prismaService,
  ): Promise<User> {
    // Create the user record
    const user = await prisma.user.create({
      data: {
        email: payload.email,
        name: payload.name,
        roleId: roleId,
        isVerified: false,
        verificationToken: this.generateEmailVerificationToken(),
      },
    });

    // Create associated password entry
    await this.passwordService.createOrUpadatePassword(user.id, payload.password, prisma);
    return user;
  }

  generateEmailVerificationToken(): string {
    return HashService.generateToken();
  }

  private async checkIfUserExistsAlready(email: string): Promise<void> {
    const user = await this.prismaService.user.findUnique({
      where: { email },
    });

    if (user) throw new ConflictException('The email address is already in use');
  }

  private async getInvitationOrThrow(token: string, email: string): Promise<TenantInvitation> {
    const invitation = await this.prismaService.tenantInvitation.findUnique({
      where: { token },
      include: { tenant: true },
    });

    // Validate invitation exists
    if (!invitation) throw new BadRequestException('Invalid invitation token');

    // Check if invitation is expired
    if (invitation.expiresAt < new Date()) throw new BadRequestException('Invitation expired');

    // Verify email matches invitation
    if (invitation.email !== email) throw new BadRequestException('Email does not match invitation');

    return invitation;
  }

  private handleUserCreationError(error: any, defaultMessage: string): never {
    this.logger.error(defaultMessage, error);

    // Preserve HTTP exceptions
    if (error instanceof HttpException) {
      throw new HttpException(error.message, error.getStatus());
    }

    // Default to internal server error
    throw new InternalServerErrorException(defaultMessage);
  }
}
