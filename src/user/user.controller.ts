import { Body, Controller, Delete, Get, HttpStatus, Param, Put, Query, Request, UnauthorizedException, UseGuards } from '@nestjs/common';
import {
  ApiBody,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiNotImplementedResponse,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { ChangePasswordDto, UserResponseDto, UserResponseWithRoleDto } from './dto/user.dto';
import { UserService } from './user.service';
import { ZodSerializerDto } from 'nestjs-zod';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { User } from '@prisma/client';
import { HttpStatusCode } from 'axios';

@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ZodSerializerDto(UserResponseWithRoleDto)
  @ApiResponse({ type: UserResponseDto })
  @ApiNotFoundResponse({ description: 'The user record does not exist' })
  async getProfile(@Request() req: { user: User }): Promise<UserResponseWithRoleDto> {
    const { user } = req;
    return await this.userService.getUserById(user.id);
  }

  // - Implement profile image update functionality

  @Delete('me')
  @UseGuards(JwtAuthGuard)
  @ApiOkResponse({ description: 'User deleted successfully' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  async deleteProfile(@Request() req: { user: User }) {
    const { user } = req;
    await this.userService.deleteUser(user.id);
    return { message: 'User deleted successfully' };
  }

  @Put('me/password')
  @UseGuards(JwtAuthGuard)
  @ApiOkResponse({ description: 'Password changed successfully' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  async changePassword(@Body() payload: ChangePasswordDto, @Request() req: { user: User }) {
    const { user } = req;
    await this.userService.changePassword(user.id, payload.newPassword, payload.oldPassword);
    return { message: 'Password changed successfully' };
  }

  @Put('/me/avatar')
  @ApiOperation({ summary: 'Has not Implemented yet' })
  @ApiNotImplementedResponse({ description: 'Not Implemented' })
  async updateProfileImage() {
    return HttpStatusCode.NotImplemented;
  }

  //super-admin only routes

  // get user by id
  @Get(':id')
  @ApiOkResponse({ description: 'User details', type: UserResponseDto })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  @ApiNotFoundResponse({ description: 'User not found' })
  @ZodSerializerDto(UserResponseDto)
  async getUserById(@Param('id') id: string): Promise<UserResponseDto> {
    return await this.userService.getUserById(id);
  }
}
