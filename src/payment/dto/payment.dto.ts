import { BillingInterval, Currency, PaymentGateway, TransactionStatus } from '@prisma/client';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

// Common schemas
const paymentGatewaySchema = z.preprocess(val => (typeof val === 'string' ? val.toUpperCase() : val), z.nativeEnum(PaymentGateway));

export type IPaymentGateway = z.infer<typeof paymentGatewaySchema>;

// Base payment schema for all gateways
export const BasePaymentSchema = z.object({
  tenantId: z.string().uuid().describe('Tenant ID for the payment'),
  planId: z.string().uuid().describe('Plan ID for the subscription'),
  paymentGateway: paymentGatewaySchema.describe('Payment gateway to use'),
  isPrimary: z.boolean().default(true).optional().describe('Whether this is the primary payment method'),
  currency: z.nativeEnum(Currency).describe('Currency for the payment'),
});

// ==================== ESEWA SCHEMAS ====================
export const EsewaVerifyPayloadSchema = z.object({
  transaction_code: z.string().describe('Transaction code from Esewa'),
  status: z.string().describe('Payment status from Esewa'),
  total_amount: z
    .string()
    .transform(val => val.replace(/,/g, ''))
    .describe('Total payment amount'),
  transaction_uuid: z.string().uuid().describe('Transaction UUID'),
  product_code: z.string().describe('Merchant product code'),
  signed_field_names: z.string().describe('Names of signed fields'),
  signature: z.string().describe('HMAC signature'),
});

export const EsewaInitiateResponseSchema = z.object({
  amount: z.union([z.string(), z.number()]).describe('Payment amount'),
  tax_amount: z.union([z.string(), z.number()]).describe('Tax amount'),
  total_amount: z.union([z.string(), z.number()]).describe('Total amount including tax'),
  transaction_uuid: z.string().uuid().describe('Transaction UUID'),
  product_code: z.string().describe('Merchant product code'),
  product_service_charge: z.union([z.string(), z.number()]).describe('Service charge'),
  product_delivery_charge: z.union([z.string(), z.number()]).describe('Delivery charge'),
  success_url: z.string().url().describe('Success redirect URL'),
  failure_url: z.string().url().describe('Failure redirect URL'),
  signed_field_names: z.string().describe('Names of signed fields'),
  signature: z.string().describe('HMAC signature'),
});

export const EsewaVerifyResponseSchema = z.object({
  status: z.string().describe('Payment status'),
  transaction_details: z.record(z.any()).describe('Transaction details'),
});

// ==================== KHALTI SCHEMAS ====================
export const KhaltiVerifySchema = z.object({
  pidx: z.string().describe('Payment ID from Khalti'),
});

export const KhaltiInitiateResponseSchema = z.object({
  pidx: z.string().uuid().describe('Payment ID from Khalti'),
  payment_url: z.string().url().describe('Payment URL for redirect'),
  expires_at: z.string().datetime().describe('Expiration time for the payment'),
  status: z.string().describe('Payment status'),
});

export const KhaltiVerifyResponseSchema = z.object({
  pidx: z.string().uuid().describe('Payment ID from Khalti'),
  total_amount: z.number().describe('Total payment amount in paisa'),
  status: z.string().describe('Payment status'),
  transaction_id: z.string().optional().describe('Transaction ID'),
  fee: z.number().optional().describe('Payment processing fee'),
  refunded: z.boolean().optional().describe('Whether payment was refunded'),
});

// ==================== STRIPE SCHEMAS ====================
export const StripeVerifyPayloadSchema = z.object({
  session_id: z.string().startsWith('cs_').describe('Stripe session ID'),
});

export const StripeInitiateResponseSchema = z.object({
  id: z.string().describe('Stripe session ID'),
  url: z.string().url().describe('Checkout URL'),
  payment_status: z.string().optional().describe('Payment status'),
  subscription: z.string().optional().describe('Subscription ID'),
  customer: z.string().optional().describe('Customer ID'),
});

export const StripeVerifyResponseSchema = z.object({
  status: z.string().describe('Payment status'),
  sessionId: z.string().describe('Stripe session ID'),
  customerId: z.union([z.string(), z.null()]).describe('Stripe customer ID'),
  subscriptionId: z.union([z.string(), z.null()]).describe('Stripe subscription ID'),
  metadata: z.record(z.any()).nullable().describe('Session metadata'),
});

export const StripeWebhookEventSchema = z.object({
  id: z.string().describe('Event ID'),
  type: z.string().describe('Event type'),
  data: z
    .object({
      object: z.record(z.any()).describe('Event data object'),
    })
    .describe('Event data'),
  created: z.number().describe('Event creation timestamp'),
});

// ==================== GENERIC PAYMENT SCHEMAS ====================

// Generic payment response schema
export const PaymentResponseSchema = z.object({
  success: z.boolean().describe('Whether the operation was successful'),
  message: z.string().optional().describe('Response message'),
  data: z.record(z.any()).optional().describe('Response data'),
  error: z.string().optional().describe('Error message if any'),
  transactionId: z.string().optional().describe('Transaction ID'),
  redirectUrl: z.string().url().optional().describe('URL to redirect to'),
  status: z.nativeEnum(TransactionStatus).optional().describe('Transaction status'),
});

// ==================== INITIATE PAYMENT DTO ====================
export const InitiatePaymentSchema = BasePaymentSchema.extend({
  // Add gateway-specific fields if needed
  returnUrl: z.string().url().optional().describe('URL to return to after payment'),
  cancelUrl: z.string().url().optional().describe('URL to return to if payment is cancelled'),
  billingInterval: z.nativeEnum(BillingInterval).optional().describe('Billing interval for subscriptions'),
});

export class InitiatePaymentDto extends createZodDto(InitiatePaymentSchema) {}

// ==================== VERIFY PAYMENT DTO ====================
// Base schema with just the payment gateway
const BaseVerifyPaymentSchema = z.object({
  paymentGateway: paymentGatewaySchema.describe('Selected payment gateway for transaction verification'),
});

// Esewa specific schema
export const EsewaPaymentSchema = BaseVerifyPaymentSchema.extend({
  paymentGateway: z.literal(PaymentGateway.ESEWA),
  data: z.string().base64().describe('Base64 encoded data for verification of esewa'),
});

// Stripe specific schema
export const StripePaymentSchema = BaseVerifyPaymentSchema.extend({
  paymentGateway: z.literal(PaymentGateway.STRIPE),
  session_id: z.string().startsWith('cs_').describe('Stripe session ID for stripe'),
});

// Khalti specific schema
export const KhaltiPaymentSchema = BaseVerifyPaymentSchema.extend({
  paymentGateway: z.literal(PaymentGateway.KHALTI),
  pidx: z.string().describe('Payment ID from Khalti'),
});

// Create separate DTOs for each payment type
export class EsewaPaymentDto extends createZodDto(EsewaPaymentSchema) {}
export class StripePaymentDto extends createZodDto(StripePaymentSchema) {}
export class KhaltiPaymentDto extends createZodDto(KhaltiPaymentSchema) {}

// Use type union for controller methods
export type VerifyPaymentDto = EsewaPaymentDto | StripePaymentDto | KhaltiPaymentDto;

// ==================== WEBHOOK DTO ====================
export const WebhookPayloadSchema = z.object({
  signature: z.string().optional().describe('Webhook signature'),
  payload: z.union([z.string(), z.record(z.any())]).describe('Webhook payload'),
  gateway: paymentGatewaySchema.describe('Payment gateway'),
});

export class WebhookPayloadDto extends createZodDto(WebhookPayloadSchema) {}

// ==================== EXPORT TYPES ====================
export type IKhaltiVerifypayload = z.infer<typeof KhaltiVerifySchema>;
export type IEsewaVerifyPayload = z.infer<typeof EsewaVerifyPayloadSchema>;
export type IStripeVerifyPayload = z.infer<typeof StripeVerifyPayloadSchema>;
export type IEsewaInitiateResponse = z.infer<typeof EsewaInitiateResponseSchema>;
export type IKhaltiInitiateResponse = z.infer<typeof KhaltiInitiateResponseSchema>;
export type IKhaltiVerifyResponse = z.infer<typeof KhaltiVerifyResponseSchema>;
export type IStripeInitiateResponse = z.infer<typeof StripeInitiateResponseSchema>;
export type IStripeVerifyResponse = z.infer<typeof StripeVerifyResponseSchema>;
export type IStripeWebhookEvent = z.infer<typeof StripeWebhookEventSchema>;
export type IPaymentResponse = z.infer<typeof PaymentResponseSchema>;
