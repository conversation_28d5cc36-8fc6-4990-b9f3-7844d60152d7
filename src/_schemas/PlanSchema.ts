import { z } from 'zod';
import { JsonValueSchema } from './JsonValueSchema';
import { SubscriptionPlan } from '@prisma/client';

/////////////////////////////////////////
// PLAN SCHEMA
/////////////////////////////////////////

export const PlanSchema = z.object({
  name: z.nativeEnum(SubscriptionPlan),
  id: z.string().uuid(),
  description: z.string().nullable(),
  features: JsonValueSchema.nullable(),
  isActive: z.boolean(),
  isPopular: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
});

export type Plan = z.infer<typeof PlanSchema>;

export default PlanSchema;
