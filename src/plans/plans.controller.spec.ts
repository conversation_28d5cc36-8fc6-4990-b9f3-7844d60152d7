import { Test, TestingModule } from '@nestjs/testing';
import { BillingInterval, Currency, SubscriptionPlan } from '@prisma/client';
import { NotFoundException } from '@nestjs/common';

// Mock the controller dependencies
jest.mock('./plans.service');
jest.mock('../auth/decorators/publicEnpoint.decorator', () => ({
  PublicEndpoint: () => jest.fn(),
}));
jest.mock('../auth/decorators/roles.decorator', () => ({
  Roles: () => jest.fn(),
}));
jest.mock('./decorators/plan-query-filter.decorator', () => ({
  ApiPlanQueryFilter: () => jest.fn(),
}));
jest.mock('@nestjs/swagger', () => ({
  ApiOperation: () => jest.fn(),
  ApiParam: () => jest.fn(),
  ApiResponse: () => jest.fn(),
}));

// Import after mocks
import { PlansController } from './plans.controller';
import { PlansService } from './plans.service';

describe('PlansController', () => {
  let controller: PlansController;
  let plansService: PlansService;

  // Mock processed plans data
  const mockProcessedPlans = [
    {
      id: '1',
      name: SubscriptionPlan.BASIC,
      description: 'Basic plan',
      amount: 1500,
      interval: BillingInterval.MONTHLY,
      durationInDays: 30,
      features: { users: 5 },
      isActive: true,
      isPopular: false,
      currency: Currency.NPR,
    },
    {
      id: '2',
      name: SubscriptionPlan.PREMIUM,
      description: 'Premium plan',
      amount: 3000,
      interval: BillingInterval.MONTHLY,
      durationInDays: 30,
      features: { users: 10 },
      isActive: true,
      isPopular: true,
      currency: Currency.NPR,
    },
  ];

  // Mock USD plan with tax
  const mockUsdPlan = {
    id: '1',
    name: SubscriptionPlan.BASIC,
    description: 'Basic plan',
    amount: 12.75, // Converted and taxed amount
    interval: BillingInterval.MONTHLY,
    durationInDays: 30,
    features: { users: 5 },
    isActive: true,
    isPopular: false,
    currency: Currency.USD,
    taxRate: 0.13,
    taxAmount: 1.47,
    originalAmount: 11.28,
  };

  // Mock for PlansService
  const mockPlansService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PlansController],
      providers: [
        {
          provide: PlansService,
          useValue: mockPlansService,
        },
      ],
    }).compile();

    controller = module.get<PlansController>(PlansController);
    plansService = module.get<PlansService>(PlansService);

    // Reset mocks before each test
    jest.clearAllMocks();
    mockPlansService.findAll.mockReset();
    mockPlansService.findOne.mockReset();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getPlans', () => {
    it('should return all plans with NPR currency by default', async () => {
      // Arrange
      mockPlansService.findAll.mockResolvedValue(mockProcessedPlans);

      // Act
      const result = await controller.getPlans({});

      // Assert
      expect(mockPlansService.findAll).toHaveBeenCalledWith({});
      expect(result).toEqual(mockProcessedPlans);
      expect(result).toHaveLength(2);
      expect(result[0].currency).toBe(Currency.NPR);
      expect(result[1].currency).toBe(Currency.NPR);
    });

    it('should return plans filtered by interval', async () => {
      // Arrange
      mockPlansService.findAll.mockResolvedValue([mockProcessedPlans[0]]);
      const filter = { interval: BillingInterval.MONTHLY };

      // Act
      const result = await controller.getPlans(filter);

      // Assert
      expect(mockPlansService.findAll).toHaveBeenCalledWith(filter);
      expect(result).toHaveLength(1);
      expect(result[0].interval).toBe(BillingInterval.MONTHLY);
    });

    it('should return plans with USD currency when requested', async () => {
      // Arrange
      const usdPlans = [
        { ...mockProcessedPlans[0], currency: Currency.USD, taxRate: 0.13, taxAmount: 1.47, originalAmount: 11.28, amount: 12.75 },
        { ...mockProcessedPlans[1], currency: Currency.USD, taxRate: 0.13, taxAmount: 2.93, originalAmount: 22.5, amount: 25.43 },
      ];
      mockPlansService.findAll.mockResolvedValue(usdPlans);
      const filter = { currency: Currency.USD };

      // Act
      const result = await controller.getPlans(filter);

      // Assert
      expect(mockPlansService.findAll).toHaveBeenCalledWith(filter);
      expect(result).toHaveLength(2);
      expect(result[0].currency).toBe(Currency.USD);
      expect(result[0].taxRate).toBe(0.13);
      expect(result[0].taxAmount).toBe(1.47);
      expect(result[0].originalAmount).toBe(11.28);
      expect(result[1].currency).toBe(Currency.USD);
    });

    it('should handle NotFoundException from service', async () => {
      // Arrange
      mockPlansService.findAll.mockRejectedValue(new NotFoundException('No plans found'));

      // Act & Assert
      await expect(controller.getPlans({})).rejects.toThrow(NotFoundException);
      expect(mockPlansService.findAll).toHaveBeenCalled();
    });
  });

  describe('getPlan', () => {
    it('should return a single plan with NPR currency by default', async () => {
      // Arrange
      mockPlansService.findOne.mockResolvedValue(mockProcessedPlans[0]);

      // Act
      const result = await controller.getPlan({ id: '1' }, {});

      // Assert
      expect(mockPlansService.findOne).toHaveBeenCalledWith('1', {});
      expect(result).toEqual(mockProcessedPlans[0]);
      expect(result.id).toBe('1');
      expect(result.currency).toBe(Currency.NPR);
    });

    it('should return a plan with USD currency when requested', async () => {
      // Arrange
      mockPlansService.findOne.mockResolvedValue(mockUsdPlan);
      const filter = { currency: Currency.USD };

      // Act
      const result = await controller.getPlan({ id: '1' }, filter);

      // Assert
      expect(mockPlansService.findOne).toHaveBeenCalledWith('1', filter);
      expect(result).toEqual(mockUsdPlan);
      expect(result.currency).toBe(Currency.USD);
      expect(result.taxRate).toBe(0.13);
      expect(result.taxAmount).toBe(1.47);
      expect(result.originalAmount).toBe(11.28);
    });

    it('should handle NotFoundException from service', async () => {
      // Arrange
      mockPlansService.findOne.mockRejectedValue(new NotFoundException('Plan not found'));

      // Act & Assert
      await expect(controller.getPlan({ id: '999' }, {})).rejects.toThrow(NotFoundException);
      expect(mockPlansService.findOne).toHaveBeenCalled();
    });
  });
});
