import { z } from 'zod';

/////////////////////////////////////////
// REFRESH TOKEN SCHEMA
/////////////////////////////////////////

export const RefreshTokenSchema = z.object({
  id: z.string().uuid(),
  token: z.string(),
  expiresAt: z.coerce.date(),
  isRevoked: z.boolean(),
  userId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type RefreshToken = z.infer<typeof RefreshTokenSchema>

export default RefreshTokenSchema;
