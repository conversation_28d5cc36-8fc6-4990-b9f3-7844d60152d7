/*
  Warnings:

  - You are about to drop the column `name` on the `pricing` table. All the data in the column will be lost.
  - You are about to drop the column `plan` on the `subscriptions` table. All the data in the column will be lost.
  - Added the required column `plan_id` to the `pricing` table without a default value. This is not possible if the table is not empty.
  - Added the required column `pricing_id` to the `subscriptions` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "TransactionType" AS ENUM ('CHARGE', 'REFUND', 'TRIAL', 'ADJUSTMENT');

-- CreateEnum
CREATE TYPE "BillingInterval" AS ENUM ('MONTHLY', 'YEARLY', 'LIFETIME');

-- DropForeignKey
ALTER TABLE "transactions" DROP CONSTRAINT "transactions_payment_method_id_fkey";

-- DropForeignKey
ALTER TABLE "transactions" DROP CONSTRAINT "transactions_subscription_id_fkey";

-- DropIndex
DROP INDEX "pricing_name_idx";

-- DropIndex
DROP INDEX "pricing_name_key";

-- DropIndex
DROP INDEX "subscriptions_plan_idx";

-- AlterTable
ALTER TABLE "pricing" DROP COLUMN "name",
ADD COLUMN     "interval" "BillingInterval" NOT NULL DEFAULT 'MONTHLY',
ADD COLUMN     "plan_id" UUID NOT NULL,
ADD COLUMN     "region_code" TEXT NOT NULL DEFAULT 'NP';

-- AlterTable
ALTER TABLE "subscriptions" DROP COLUMN "plan",
ADD COLUMN     "pricing_id" UUID NOT NULL;

-- AlterTable
ALTER TABLE "transactions" ADD COLUMN     "transaction_type" "TransactionType" NOT NULL DEFAULT 'CHARGE',
ALTER COLUMN "payment_method_id" DROP NOT NULL,
ALTER COLUMN "subscription_id" DROP NOT NULL,
ALTER COLUMN "purchase_order_id" DROP NOT NULL,
ALTER COLUMN "purchase_order_name" DROP NOT NULL;

-- CreateTable
CREATE TABLE "plans" (
    "id" UUID NOT NULL,
    "name" "SubscriptionPlan" NOT NULL,
    "description" TEXT,
    "features" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isPopular" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "plans_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "plans_name_key" ON "plans"("name");

-- CreateIndex
CREATE INDEX "plans_name_idx" ON "plans"("name");

-- CreateIndex
CREATE INDEX "pricing_plan_id_region_code_idx" ON "pricing"("plan_id", "region_code");

-- CreateIndex
CREATE INDEX "subscriptions_pricing_id_idx" ON "subscriptions"("pricing_id");

-- CreateIndex
CREATE INDEX "transactions_transaction_type_idx" ON "transactions"("transaction_type");

-- AddForeignKey
ALTER TABLE "subscriptions" ADD CONSTRAINT "subscriptions_pricing_id_fkey" FOREIGN KEY ("pricing_id") REFERENCES "pricing"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_payment_method_id_fkey" FOREIGN KEY ("payment_method_id") REFERENCES "payment_method"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_subscription_id_fkey" FOREIGN KEY ("subscription_id") REFERENCES "subscriptions"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pricing" ADD CONSTRAINT "pricing_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "plans"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
