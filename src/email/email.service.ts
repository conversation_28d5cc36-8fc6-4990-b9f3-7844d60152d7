import { Injectable, Logger } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class EmailService {
  private logger = new Logger(EmailService.name);
  constructor(
    private readonly mailerService: MailerService,
    private readonly configService: ConfigService,
    @InjectQueue('emailQueue') private emailQueue: Queue,
  ) {}

  private CLIENT_BASE_URL = this.configService.getOrThrow<string>('CLIENT_BASE_URL');
  private async queueEmail(to: string, subject: string, template: string, context: object): Promise<void> {
    try {
      await this.emailQueue.add(
        'send-email',
        {
          to,
          subject,
          template,
          context,
        },
        {
          attempts: 3, // Retry up to 3 times
          backoff: {
            type: 'exponential',
            delay: 5000, // Start with 5 seconds delay
          },
          removeOnComplete: true,
          removeOnFail: false, // Keep failed jobs for inspection
        },
      );
      this.logger.log(`Email to ${to} with subject "${subject}" queued successfully`);
    } catch (error) {
      this.logger.error(`Failed to queue email to ${to}`, error);
      throw new Error('Email queueing failed');
    }
  }

  public async sendVerificationEmail(to: string, customerName: string, verificationToken: string | null): Promise<void> {
    const confirmationLink = `${this.CLIENT_BASE_URL}/verify/${verificationToken}`;
    const subject = 'Verify your Email Address';
    await this.queueEmail(to, subject, './verificationEmail', { customerName, confirmationLink });
  }

  public async sendWelcomeEmail(to: string, customerName: string): Promise<void> {
    const subject = 'Welcome to our platform';
    const getStartedLink = `${this.CLIENT_BASE_URL}/login`;
    await this.queueEmail(to, subject, './welcomeEmail', { customerName, getStartedLink });
  }

  public async sendInvitationEmail(
    to: string,
    invitationToken: string | null,
    inviterName: string,
    organizationName: string,
    expirationDate: Date,
  ): Promise<void> {
    const invitationLink = `${this.CLIENT_BASE_URL}/signup?invitation_token=${invitationToken}`;
    const subject = 'Invitation to join our platform';
    await this.queueEmail(to, subject, './invitationEmail', {
      invitationLink,
      inviterName,
      organizationName,
      expirationDate,
      recipientEmail: to,
    });
  }

  public async sendPasswordResetEmail(to: string, userName: string, userId: string, resetToken: string | null): Promise<void> {
    const resetLink = `${this.CLIENT_BASE_URL}/reset/${userId}/${resetToken}`;
    const subject = 'Reset your Password';
    await this.queueEmail(to, subject, './passwordResetEmail', { customerName: userName, resetLink });
  }

  public async sendPasswordResetSuccessEmail(to: string, customerName: string): Promise<void> {
    const subject = 'Password Reset Successful';

    await this.queueEmail(to, subject, './passwordResetSuccessEmail', { customerName });
  }

  async sendEmail(to: string, subject: string, template: string, context: object) {
    try {
      await this.mailerService.sendMail({
        to,
        subject,
        template,
        context,
      });
      this.logger.log(`${subject} email sent successfully`);
    } catch (error) {
      this.logger.error(`Failed to send ${subject} email to ${to}`, error);
      throw new Error('Email sending failed');
    }
  }
}
