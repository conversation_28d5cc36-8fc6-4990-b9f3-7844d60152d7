import { z } from 'zod';
import { Injectable, ArgumentMetadata, BadRequestException } from '@nestjs/common';

abstract class ZodDtoClass<T extends z.ZodSchema> {
  static schema: z.ZodSchema;
  data: z.infer<T>;
}

@Injectable()
export class CustomZodValidationPipe {
  transform(value: any, metadata: ArgumentMetadata) {
    const schemaClass: typeof ZodDtoClass = metadata.metatype! as unknown as typeof ZodDtoClass;

    // Add this check
    if (!schemaClass.schema) {
      return value; // Or handle appropriately
    }
    const result = schemaClass.schema.safeParse(value);
    if (!result.success) {
      throw new BadRequestException(result.error.flatten());
    }
    return result.data;
  }
}
