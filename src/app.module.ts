import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { ZodSerializerInterceptor, ZodValidationPipe } from 'nestjs-zod';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PrismaModule } from './prisma/prisma.module';
import { AuthModule } from './auth/auth.module';
import { PasswordModule } from './password/password.module';
import { UserModule } from './user/user.module';
import { NodemailerModule } from './email/email.module';
import { TenantModule } from './tenant/tenant.module';
import { JwtModule } from '@nestjs/jwt';

import { PrismaExceptionFilter } from './common/filters/prisma-exception.filter';
import { PlansModule } from './plans/plans.module';
import { CustomZodValidationPipe } from './_pipe/customZodValidationPipe';
import { JwtAuthGuard } from './auth/guard/jwt-auth.guard';
import { PaymentModule } from './payment/payment.module';
import { RolesGuard } from './auth/guard/roles.guard';
import { SubscriptionModule } from './subscription/subscription.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    PrismaModule,
    AuthModule,
    PasswordModule,
    UserModule,
    NodemailerModule,
    TenantModule,
    PaymentModule,
    SubscriptionModule,
    JwtModule.registerAsync({
      global: true, // Makes JWT available across all modules
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: await configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRATION', '60s'), // Default to 60s
        },
      }),
      inject: [ConfigService],
    }),
    PlansModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_PIPE,
      useClass: CustomZodValidationPipe,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ZodSerializerInterceptor,
    },
    {
      provide: APP_FILTER,
      useFactory: () => new PrismaExceptionFilter(),
    },
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
  ],
})
export class AppModule {}
