/*
  Warnings:

  - The `verification_token` column on the `users` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - A unique constraint covering the columns `[user_id]` on the table `access_tokens` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[user_id]` on the table `tenant_users` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "users" DROP COLUMN "verification_token",
ADD COLUMN     "verification_token" UUID;

-- CreateIndex
CREATE UNIQUE INDEX "access_tokens_user_id_key" ON "access_tokens"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "tenant_users_user_id_key" ON "tenant_users"("user_id");
