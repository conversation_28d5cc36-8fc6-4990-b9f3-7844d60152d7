import { PermissionKey } from '@prisma/client';
import { z } from 'zod';

/////////////////////////////////////////
// PERMISSION SCHEMA
/////////////////////////////////////////

export const PermissionSchema = z.object({
  name: z.nativeEnum(PermissionKey),
  id: z.string().uuid(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
});

export type Permission = z.infer<typeof PermissionSchema>;

export default PermissionSchema;
