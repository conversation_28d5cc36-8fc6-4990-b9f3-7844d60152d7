/*
  Warnings:

  - The values [CREDIT_CARD,FONE_PAY,CONNECT_IPS] on the enum `PaymentGateway` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `pricing_id` on the `subscriptions` table. All the data in the column will be lost.
  - You are about to drop the column `pricing_id` on the `transactions` table. All the data in the column will be lost.
  - You are about to drop the `access_tokens` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `plans` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `pricing` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `plan_id` to the `subscriptions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `plan_id` to the `transactions` table without a default value. This is not possible if the table is not empty.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "PaymentGateway_new" AS ENUM ('STRIPE', 'ESEWA', 'KHALTI', 'FONEPAY');
ALTER TABLE "payment_method" ALTER COLUMN "payment_gateway" TYPE "PaymentGateway_new" USING ("payment_gateway"::text::"PaymentGateway_new");
ALTER TYPE "PaymentGateway" RENAME TO "PaymentGateway_old";
ALTER TYPE "PaymentGateway_new" RENAME TO "PaymentGateway";
DROP TYPE "PaymentGateway_old";
COMMIT;

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "SubscriptionPlan" ADD VALUE 'BASIC_YEARLY';
ALTER TYPE "SubscriptionPlan" ADD VALUE 'PREMIUM_YEARLY';
ALTER TYPE "SubscriptionPlan" ADD VALUE 'PLATINUM_YEARLY';

-- DropForeignKey
ALTER TABLE "access_tokens" DROP CONSTRAINT "access_tokens_user_id_fkey";

-- DropForeignKey
ALTER TABLE "pricing" DROP CONSTRAINT "pricing_plan_id_fkey";

-- DropForeignKey
ALTER TABLE "subscriptions" DROP CONSTRAINT "subscriptions_pricing_id_fkey";

-- DropForeignKey
ALTER TABLE "transactions" DROP CONSTRAINT "transactions_pricing_id_fkey";

-- DropIndex
DROP INDEX "subscriptions_pricing_id_idx";

-- AlterTable
ALTER TABLE "payment_method" ALTER COLUMN "is_primary" SET DEFAULT false;

-- AlterTable
ALTER TABLE "subscriptions" DROP COLUMN "pricing_id",
ADD COLUMN     "plan_id" UUID NOT NULL;

-- AlterTable
ALTER TABLE "transactions" DROP COLUMN "pricing_id",
ADD COLUMN     "plan_id" UUID NOT NULL;

-- DropTable
DROP TABLE "access_tokens";

-- DropTable
DROP TABLE "plans";

-- DropTable
DROP TABLE "pricing";

-- CreateTable
CREATE TABLE "plan" (
    "id" UUID NOT NULL,
    "name" "SubscriptionPlan" NOT NULL,
    "description" TEXT,
    "amount" INTEGER NOT NULL,
    "interval" "BillingInterval" NOT NULL,
    "duration_in_days" INTEGER NOT NULL,
    "features" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isPopular" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "plan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "refresh_tokens" (
    "id" UUID NOT NULL,
    "token" UUID NOT NULL,
    "expires_at" TIMESTAMP(3) NOT NULL,
    "is_revoked" BOOLEAN NOT NULL DEFAULT false,
    "user_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "refresh_tokens_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "plan_name_key" ON "plan"("name");

-- CreateIndex
CREATE INDEX "plan_name_idx" ON "plan"("name");

-- CreateIndex
CREATE UNIQUE INDEX "refresh_tokens_token_key" ON "refresh_tokens"("token");

-- CreateIndex
CREATE UNIQUE INDEX "refresh_tokens_user_id_key" ON "refresh_tokens"("user_id");

-- CreateIndex
CREATE INDEX "refresh_tokens_user_id_idx" ON "refresh_tokens"("user_id");

-- CreateIndex
CREATE INDEX "subscriptions_plan_id_idx" ON "subscriptions"("plan_id");

-- AddForeignKey
ALTER TABLE "subscriptions" ADD CONSTRAINT "subscriptions_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "plan"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "plan"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "refresh_tokens" ADD CONSTRAINT "refresh_tokens_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
