import { Injectable, NotFoundException, ConflictException, BadRequestException, Logger, ForbiddenException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateTenantDto, InviteToTenantDto } from './dto/tenant.dto';
import { SystemRoles } from '@prisma/client';
import { randomUUID } from 'crypto';
import { ConfigService } from '@nestjs/config';
import { EmailService } from 'src/email/email.service';

@Injectable()
export class TenantService {
  private readonly logger = new Logger(TenantService.name);

  private readonly INVITATION_TOKEN_EXPIRES_AT = 3600 * 24 * 7 * 1000; //7 days
  private readonly TRIAL_PERIOD = 14 * 24 * 3600 * 1000; //10 days

  constructor(
    private readonly prismaService: PrismaService,
    private readonly configService: ConfigService,
    private readonly emailService: EmailService,
  ) {}

  /**
   * Creates a new tenant and assigns the creator as a tenant admin
   * @param createTenantPayload - Data for tenant creation including user ID
   * @returns The created tenant
   */
  async createTenant(createTenantPayload: CreateTenantDto, userId: string): Promise<string> {
    // Check if organization name already exists
    const existingTenant = await this.prismaService.tenant.findUnique({
      where: { organizationName: createTenantPayload.organizationName },
    });

    if (existingTenant) {
      throw new ConflictException('Organization name already exists');
    }

    //NOTE: add the ondelete cascade to the tenant and subscription table

    return await this.prismaService.$transaction(async prisma => {
      // Fetch user first
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, isVerified: true },
      });
      if (!user) throw new NotFoundException('User not found');
      if (!user.isVerified) throw new BadRequestException('User email must be verified before creating a tenant');

      // Get TENANT_ADMIN role
      const adminRole = await prisma.role.findUnique({
        where: { type: SystemRoles.TENANT_ADMIN },
        select: { id: true },
      });
      if (!adminRole) throw new BadRequestException('Tenant admin role not found');

      // Create tenant
      const tenant = await prisma.tenant.create({
        data: {
          contactNumber: createTenantPayload.contactNumber,
          organizationName: createTenantPayload.organizationName,
          roleAtOrganization: createTenantPayload.roleAtOrganization,
          organizationWebsite: createTenantPayload.organizationWebsite,
        },
      });

      // Link user to tenant as admin
      await prisma.tenantUser.create({
        data: {
          userId: user.id,
          tenantId: tenant.id,
          roleId: adminRole.id,
        },
      });

      return 'Tenant created successfully';
    });
  }

  // Helper method to get the pricing ID for the FREE plan
  private async getPricingIdForFreePlan(prisma: any): Promise<string> {
    const freePricing = await prisma.pricing.findFirst({
      where: {
        plan: {
          name: 'FREE',
        },
      },
      select: { id: true },
    });

    if (!freePricing) {
      throw new BadRequestException('Free pricing plan not found');
    }

    return freePricing.id;
  }

  async inviteUser({ userId, email: recipientEmail }: InviteToTenantDto): Promise<string> {
    // Check if user is already a member of the tenant
    const inviter = await this.prismaService.user.findFirst({
      where: { id: userId },
      include: { tenantUser: true },
    });
    if (!inviter) {
      throw new NotFoundException('User not found');
    }
    const tenantAdminRole = await this.prismaService.role.findFirst({
      where: { type: SystemRoles.TENANT_ADMIN },
    });
    const isTenantAdmin = inviter.roleId === tenantAdminRole?.id;
    if (!isTenantAdmin) {
      throw new ForbiddenException('You do not have permission to invite users to this tenant');
    }

    // Check if inviter has a tenant
    if (!inviter.tenantUser || !inviter.tenantUser.tenantId) {
      throw new BadRequestException('User does not belong to any tenant');
    }

    //check if the member already exists
    const existingMember = await this.prismaService.tenantUser.findFirst({
      where: { user: { email: recipientEmail } },
    });

    if (existingMember) {
      throw new ConflictException('User is already a member of this tenant');
    }

    // check if there's already a pending invitation
    const existingInvitation = await this.prismaService.tenantInvitation.findFirst({
      where: {
        email: recipientEmail,
        tenantId: inviter.tenantUser.tenantId,
        expiresAt: { gt: new Date() },
      },
    });

    if (existingInvitation) {
      throw new ConflictException('An invitation has already been sent to this email');
    }
    const tenant = await this.prismaService.tenant.findFirstOrThrow({
      where: { id: inviter.tenantUser.tenantId },
      select: { organizationName: true },
    });

    //create and send the invitation
    const token = randomUUID();
    const expiresAt = new Date(Date.now() + this.INVITATION_TOKEN_EXPIRES_AT);
    const tenantUserRole = await this.prismaService.role.findFirst({
      where: { type: SystemRoles.MEMBER },
    });
    const inviterName = inviter.name;
    const organizationName = tenant.organizationName;

    if (!tenantUserRole) {
      throw new BadRequestException('Member role not found');
    }

    await this.prismaService.tenantInvitation.create({
      data: {
        email: recipientEmail,
        token,
        expiresAt,
        tenantId: inviter.tenantUser.tenantId, // Use non-optional tenantId
        roleId: tenantUserRole.id,
      },
    });
    await this.emailService.sendInvitationEmail(recipientEmail, token, inviterName, organizationName, expiresAt);
    return 'Invitation sent successfully';
  }

  /**
   * Verifies an invitation token and returns invitation details
   * @param token - Invitation token
   * @returns Invitation details
   */
  async verifyInvitation(token: string) {
    const invitation = await this.prismaService.tenantInvitation.findUnique({
      where: { token },
      include: { tenant: true },
    });

    if (!invitation) {
      throw new NotFoundException('Invitation not found');
    }

    if (invitation.expiresAt < new Date()) {
      throw new BadRequestException('Invitation has expired');
    }

    return {
      email: invitation.email,
      tenant: {
        id: invitation.tenant.id,
        organizationName: invitation.tenant.organizationName,
      },
      expiresAt: invitation.expiresAt,
    };
  }

  /**
   * Connects an existing user to a tenant based on an invitation token
   * This is used after a user has signed up or logged in
   * @param token - Invitation token
   * @param userId - User ID to connect to the tenant
   * @returns Tenant user relation
   */
  async acceptInvitation(token: string, userId: string) {
    return await this.prismaService.$transaction(async prisma => {
      // Find invitation
      const invitation = await prisma.tenantInvitation.findUnique({
        where: { token },
      });

      // Check if invitation exists
      if (!invitation) throw new NotFoundException('Invitation not found');
      if (invitation.expiresAt < new Date()) throw new BadRequestException('Invitation has expired');

      // Find user
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: { tenantUser: true },
      });

      if (!user) throw new NotFoundException('User not found');
      if (!user.isVerified) throw new BadRequestException('User email must be verified before accepting invitation');

      // Verify email matches invitation
      if (user.email !== invitation.email) throw new BadRequestException('User email does not match invitation');
      if (!user.tenantUser) throw new BadRequestException('No tenant users found');

      // Check if user is already a member of the tenant
      const existingMembership = user.tenantUser.tenantId !== invitation.tenantId;
      if (existingMembership) throw new ConflictException('User is already a member of this tenant');

      // Add user to tenant
      const tenantUser = await prisma.tenantUser.create({
        data: {
          userId: user.id,
          tenantId: invitation.tenantId,
          roleId: invitation.roleId,
        },
      });

      // Delete the invitation
      await prisma.tenantInvitation.delete({
        where: { id: invitation.id },
      });

      // Get tenant details for response
      const tenant = await prisma.tenant.findUnique({
        where: { id: invitation.tenantId },
      });
      if (!tenant) {
        throw new NotFoundException('tenant not found');
      }

      return {
        tenantUser: {
          id: tenantUser.id,
          userId: tenantUser.userId,
          tenantId: tenantUser.tenantId,
        },
        tenant: {
          id: tenant.id,
          organizationName: tenant.organizationName,
        },
      };
    });
  }

  /**
   * Gets all tenants a user belongs to
   * @param userId - ID of the user
   * @returns List of tenants with role information
   */
  async getUserTenants(userId: string) {
    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
      include: {
        tenantUser: {
          include: {
            tenant: true,
            role: true,
          },
        },
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const userTenants = await this.prismaService.tenantUser.findFirst({
      where: { userId: userId },
    });

    return userTenants;
  }
}
