import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { BasePlan, ProcessedPlan, PlansFilterDto } from './dto/plans.dto';
import { Currency } from '@prisma/client';
import { convertNPRToUSD } from '../_utils/currency.utils';

@Injectable()
export class PlansService {
  constructor(private readonly prismaService: PrismaService) {}

  // Tax rate for USD prices (13%)
  private readonly USD_TAX_RATE = 0.13;

  //get available plans
  async findAll(filter: PlansFilterDto): Promise<ProcessedPlan[]> {
    const { currency, interval } = filter;

    const plans = await this.prismaService.plan.findMany({
      where: {
        isActive: true,
        interval: interval,
      },
      omit: {
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        id: 'asc',
      },
    });

    if (!plans || plans.length <= 0) {
      throw new NotFoundException('No plans found');
    }

    // Process plans based on requested currency
    const processedPlans = plans.map(plan => {
      // Create a copy of the plan to avoid modifying the original
      const processedPlan: ProcessedPlan = {
        ...(plan as unknown as BasePlan),
        currency: currency || Currency.NPR,
      };

      // If USD is requested, convert the amount from NPR to USD and add tax
      if (processedPlan.currency === Currency.USD) {
        // Convert amount from NPR to USD
        const usdAmount = convertNPRToUSD(Number(processedPlan.amount));

        // Add tax to USD amount
        const taxAmount = usdAmount * this.USD_TAX_RATE;
        const totalAmount = usdAmount + taxAmount;

        // Update the amount with the taxed USD value
        processedPlan.amount = totalAmount;

        // Add tax information to the plan
        processedPlan.taxRate = this.USD_TAX_RATE;
        processedPlan.taxAmount = taxAmount;
        processedPlan.originalAmount = usdAmount;
      }

      return processedPlan;
    });

    return processedPlans;
  }

  async findOne(id: string, filter?: PlansFilterDto): Promise<ProcessedPlan> {
    const plan = await this.prismaService.plan.findFirstOrThrow({
      where: {
        id,
      },
      omit: {
        createdAt: true,
        updatedAt: true,
      },
    });

    // If no filter is provided or no currency is specified, return the plan with NPR currency
    if (!filter || !filter.currency) {
      // Default to NPR
      return {
        ...(plan as unknown as BasePlan),
        currency: Currency.NPR,
      };
    }

    // Create a processed plan with the requested currency
    const processedPlan: ProcessedPlan = {
      ...(plan as unknown as BasePlan),
      currency: filter.currency,
    };

    // If USD is requested, convert the amount from NPR to USD and add tax
    if (filter.currency === Currency.USD) {
      // Convert amount from NPR to USD
      const usdAmount = convertNPRToUSD(Number(processedPlan.amount));

      // Add tax to USD amount
      const taxAmount = usdAmount * this.USD_TAX_RATE;
      const totalAmount = usdAmount + taxAmount;

      // Update the amount with the taxed USD value
      processedPlan.amount = totalAmount;

      // Add tax information to the plan
      processedPlan.taxRate = this.USD_TAX_RATE;
      processedPlan.taxAmount = taxAmount;
      processedPlan.originalAmount = usdAmount;
    }

    return processedPlan;
  }
  //TODO: Implement Create,Update and Delete Plans for super admin
}
