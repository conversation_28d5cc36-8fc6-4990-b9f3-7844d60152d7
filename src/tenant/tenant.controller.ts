import { Body, Controller, Post, UseGuards, Request, Logger } from '@nestjs/common';
import { CreateTenantDto, InviteEmailDto } from './dto/tenant.dto';
import { TenantService } from './tenant.service';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { ApiBadRequestResponse, ApiOkResponse, ApiParam } from '@nestjs/swagger';
import { User } from '@prisma/client';
import { EmailService } from 'src/email/email.service';
import { Roles } from 'src/auth/decorators/roles.decorator';

@Controller('tenant')
export class TenantController {
  private readonly logger = new Logger(TenantController.name);
  constructor(
    private readonly tenantService: TenantService,
    private readonly nodeMailerService: EmailService,
  ) {}

  @Post('onboarding')
  @Roles('TENANT_ADMIN')
  @ApiOkResponse({ description: 'Tenant created successfully' })
  @ApiBadRequestResponse({ description: 'Tenant creation failed' })
  async onboarding(@Request() req: { user: User }, @Body() payload: CreateTenantDto): Promise<{ message: string }> {
    const user = req.user;
    await this.tenantService.createTenant(payload, user.id);
    await this.nodeMailerService.sendWelcomeEmail(user.email, user.name);
    return { message: 'Tenant created successfully' };
  }

  @Post('invite')
  @Roles('TENANT_ADMIN')
  @ApiOkResponse({ description: 'Invitation email sent successfully' })
  @ApiBadRequestResponse({ description: 'Invitation failed' })
  @ApiParam({ name: 'email', type: 'string', required: true })
  @UseGuards(JwtAuthGuard)
  async invite(@Request() req: { user: User }, @Body() payload: InviteEmailDto): Promise<string> {
    const user = req.user;

    return await this.tenantService.inviteUser({ userId: user.id, email: payload.email });
  }
}
