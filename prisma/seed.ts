import { PrismaClient, SystemRoles, PermissionKey, Currency, SubscriptionPlan, Plan, BillingInterval } from '@prisma/client';

const prisma = new PrismaClient();

// Configuration for yearly billing
const YEARLY_DISCOUNT_RATE = 0.9; // 10% discount for yearly payments
// this can be changed

// Centralized plan configuration
const PLAN_CONFIG = {
  [SubscriptionPlan.BASIC]: {
    description: 'Basic plan with core features',
    features: {
      chatAgents: 2,
      integrations: 2,
      channels: ['facebook', 'instagram', 'whatsapp', 'telegram'],
    },
    isActive: true,
    isPopular: true,
    amount: 1500,
    interval: BillingInterval.MONTHLY,
    durationInDays: 30,
    pricing: {
      [Currency.NPR]: 1500,
      [Currency.USD]: 15,
    },
  },
  [SubscriptionPlan.PREMIUM]: {
    description: 'Includes AI, automation and more',
    features: {
      chatAgents: 4,
      includes: 'BASIC',
      aiChatbot: true,
      labelCreation: true,
      savedReply: true,
      messageAssignment: true,
      dedicatedSupport: true,
    },
    isActive: true,
    isPopular: false,
    amount: 2000,
    interval: BillingInterval.MONTHLY,
    durationInDays: 30,
    pricing: {
      [Currency.NPR]: 2000,
      [Currency.USD]: 20,
    },
  },
  [SubscriptionPlan.PLATINUM]: {
    description: 'All-in-one advanced solution',
    features: {
      chatAgents: 12,
      websiteIntegration: true,
      externalLink: true,
      deliveryPartnerIntegration: true,
    },
    isActive: true,
    isPopular: false,
    amount: 3000,
    interval: BillingInterval.MONTHLY,
    durationInDays: 30,
    pricing: {
      [Currency.NPR]: 3000,
      [Currency.USD]: 30,
    },
  },
};

// Helper function to calculate yearly amount
const getYearlyPlanAmount = (monthlyAmount: number): number => {
  return monthlyAmount * 12 * YEARLY_DISCOUNT_RATE;
};

async function main() {
  await seedSystemRoles();
  await seedPermissions();
  await assignRolePermissions();
  await seedPricingPlans();
}

async function seedSystemRoles() {
  const roles: SystemRoles[] = [SystemRoles.SUPER_ADMIN, SystemRoles.TENANT_ADMIN, SystemRoles.MEMBER, SystemRoles.GUEST];

  for (const role of roles) {
    await prisma.role.upsert({
      where: { type: role },
      update: {},
      create: { type: role },
    });
  }
  console.log('System Roles Seeded Successfully');
}

async function seedPermissions() {
  const permissions: PermissionKey[] = [
    PermissionKey.MANAGE_TENANT,
    PermissionKey.MANAGE_USERS,
    PermissionKey.MANAGE_ROLES,
    PermissionKey.VIEW_CONTENT,
    PermissionKey.EDIT_OWN_PROFILE,
    PermissionKey.MANAGE_BILLING,
    PermissionKey.ASSIGN_MESSAGES,
    PermissionKey.VIEW_ASSIGNED_MESSAGES,
    PermissionKey.DELETE_MESSAGES,
  ];

  for (const permission of permissions) {
    await prisma.permission.upsert({
      where: { name: permission },
      update: {},
      create: { name: permission },
    });
  }
  console.log('System Permissions Seeded Successfully');
}

async function assignRolePermissions() {
  const allPermissions = await prisma.permission.findMany();

  const superAdminRole = await prisma.role.findUniqueOrThrow({
    where: { type: SystemRoles.SUPER_ADMIN },
  });
  const tenantAdminRole = await prisma.role.findUniqueOrThrow({
    where: { type: SystemRoles.TENANT_ADMIN },
  });

  await prisma.rolePermission.createMany({
    skipDuplicates: true,
    data: allPermissions.map(perm => ({
      roleId: superAdminRole.id,
      permissionId: perm.id,
    })),
  });

  const tenantAdminPerms = [
    PermissionKey.MANAGE_USERS,
    PermissionKey.VIEW_CONTENT,
    PermissionKey.EDIT_OWN_PROFILE,
    PermissionKey.ASSIGN_MESSAGES,
    PermissionKey.VIEW_ASSIGNED_MESSAGES,
  ];

  const tenantAdminPermissions = await prisma.permission.findMany({
    where: { name: { in: tenantAdminPerms } },
  });

  await prisma.rolePermission.createMany({
    skipDuplicates: true,
    data: tenantAdminPermissions.map(perm => ({
      roleId: tenantAdminRole.id,
      permissionId: perm.id,
    })),
  });

  console.log('Role permissions assigned successfully');
}

async function seedPricingPlans() {
  try {
    // Create plans from the configuration
    const planEntries = Object.entries(PLAN_CONFIG).map(([planName, planDetails]) => ({
      name: planName as SubscriptionPlan,
      description: planDetails.description,
      features: planDetails.features,
      isActive: planDetails.isActive,
      isPopular: planDetails.isPopular,
      amount: planDetails.amount,
      interval: planDetails.interval,
      durationInDays: planDetails.durationInDays,
    }));

    // Upsert all plans
    for (const plan of planEntries) {
      await prisma.plan.upsert({
        where: { name: plan.name },
        update: {},
        create: plan,
      });
    }

    // Create yearly plans with specific _YEARLY enum values
    const yearlyPlans = [
      {
        name: SubscriptionPlan.BASIC_YEARLY,
        description: `${PLAN_CONFIG[SubscriptionPlan.BASIC].description} (Yearly)`,
        features: PLAN_CONFIG[SubscriptionPlan.BASIC].features,
        isActive: PLAN_CONFIG[SubscriptionPlan.BASIC].isActive,
        isPopular: PLAN_CONFIG[SubscriptionPlan.BASIC].isPopular,
        amount: getYearlyPlanAmount(PLAN_CONFIG[SubscriptionPlan.BASIC].amount),
        interval: BillingInterval.YEARLY,
        durationInDays: 365,
      },
      {
        name: SubscriptionPlan.PREMIUM_YEARLY,
        description: `${PLAN_CONFIG[SubscriptionPlan.PREMIUM].description} (Yearly)`,
        features: PLAN_CONFIG[SubscriptionPlan.PREMIUM].features,
        isActive: PLAN_CONFIG[SubscriptionPlan.PREMIUM].isActive,
        isPopular: PLAN_CONFIG[SubscriptionPlan.PREMIUM].isPopular,
        amount: getYearlyPlanAmount(PLAN_CONFIG[SubscriptionPlan.PREMIUM].amount),
        interval: BillingInterval.YEARLY,
        durationInDays: 365,
      },
      {
        name: SubscriptionPlan.PLATINUM_YEARLY,
        description: `${PLAN_CONFIG[SubscriptionPlan.PLATINUM].description} (Yearly)`,
        features: PLAN_CONFIG[SubscriptionPlan.PLATINUM].features,
        isActive: PLAN_CONFIG[SubscriptionPlan.PLATINUM].isActive,
        isPopular: PLAN_CONFIG[SubscriptionPlan.PLATINUM].isPopular,
        amount: getYearlyPlanAmount(PLAN_CONFIG[SubscriptionPlan.PLATINUM].amount),
        interval: BillingInterval.YEARLY,
        durationInDays: 365,
      },
    ];

    // Try to create yearly plans
    try {
      console.log('Creating yearly plans...');
      for (const plan of yearlyPlans) {
        await prisma.plan.upsert({
          where: { name: plan.name },
          update: {},
          create: plan,
        });
      }
    } catch (error) {
      console.error('Error creating yearly plans:', error);
      console.log('Note: Yearly plans could not be created as separate entries. This is expected if the SubscriptionPlan enum is strict.');
    }

    console.log('Pricing Plans Seeded Successfully');
  } catch (error) {
    console.error('Error seeding pricing plans:', error);
    throw error;
  }
}

main()
  .catch(e => {
    console.error(e);
    process.exit(1);
  })
  .finally(() => {
    prisma.$disconnect().catch(error => {
      console.error('Error disconnecting from database:', error);
      process.exit(1);
    });
  });
