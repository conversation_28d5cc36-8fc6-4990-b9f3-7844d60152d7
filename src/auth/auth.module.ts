import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { PrismaModule } from 'src/prisma/prisma.module';
import { PasswordModule } from 'src/password/password.module';
import { NodemailerModule } from 'src/email/email.module';
import { UserModule } from 'src/user/user.module';
import { PassportModule } from '@nestjs/passport';
import { JwtStrategy } from './strategies/jwt.strategy';
import { JwtAuthGuard } from './guard/jwt-auth.guard';

@Module({
  controllers: [AuthController],
  providers: [AuthService, JwtStrategy, JwtAuthGuard],
  imports: [PrismaModule, PasswordModule, UserModule, NodemailerModule, PassportModule.register({ defaultStrategy: 'jwt' })],
  exports: [JwtAuthGuard],
})
export class AuthModule {}
