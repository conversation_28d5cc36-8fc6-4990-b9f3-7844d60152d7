/*
  Warnings:

  - You are about to drop the column `token` on the `tenant_invitations` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[invitation_token]` on the table `tenant_invitations` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `invitation_token` to the `tenant_invitations` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "tenant_invitations_token_idx";

-- DropIndex
DROP INDEX "tenant_invitations_token_key";

-- AlterTable
ALTER TABLE "tenant_invitations" DROP COLUMN "token",
ADD COLUMN     "invitation_token" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "tenant_invitations_invitation_token_key" ON "tenant_invitations"("invitation_token");

-- CreateIndex
CREATE INDEX "tenant_invitations_invitation_token_idx" ON "tenant_invitations"("invitation_token");
