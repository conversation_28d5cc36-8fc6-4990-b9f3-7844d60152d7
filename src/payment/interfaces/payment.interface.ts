import {
  IEsewaInitiateResponse,
  IEsewaVerifyPayload,
  IKhaltiInitiateResponse,
  IKhaltiVerifypayload,
  IKhaltiVerifyResponse,
  InitiatePaymentDto,
  IPaymentResponse,
  IStripeInitiateResponse,
  IStripeVerifyResponse,
  VerifyPaymentDto,
  WebhookPayloadDto,
} from '../dto/payment.dto';

/**
 * Common interface for all payment gateways
 * Each gateway must implement these methods
 */
export interface PaymentGateway {
  /**
   * Initiate a payment transaction
   * @param payload Payment initialization data
   * @returns Payment response with redirect URL or session data
   */
  initiatePayment(
    payload: InitiatePaymentDto,
  ): Promise<IEsewaInitiateResponse | IKhaltiInitiateResponse | IStripeInitiateResponse | IPaymentResponse>;

  /**
   * Verify a payment transaction
   * @param payload Payment verification data
   * @returns Verification result
   */
  verifyPayment(
    payload: IKhaltiVerifypayload | IEsewaVerifyPayload | VerifyPaymentDto,
  ): Promise<IKhaltiVerifyResponse | IStripeVerifyResponse | IPaymentResponse>;

  /**
   * Handle webhook notifications from payment provider
   * @param signature Webhook signature for verification
   * @param payload Raw webhook payload
   * @returns Processing result
   */
  handleWebhook?(signature: string, payload: Buffer): Promise<IPaymentResponse>;
}

/**
 * Interface for subscription-capable payment gateways
 * Extends the base PaymentGateway interface
 */
export interface SubscriptionPaymentGateway extends PaymentGateway {
  /**
   * Cancel an active subscription
   * @param subscriptionId ID of the subscription to cancel
   * @returns Cancellation result
   */
  cancelSubscription?(subscriptionId: string): Promise<IPaymentResponse>;

  /**
   * Update an existing subscription
   * @param subscriptionId ID of the subscription to update
   * @param updateData Data to update on the subscription
   * @returns Update result
   */
  updateSubscription?<T>(subscriptionId: string, updateData: T): Promise<IPaymentResponse>;

  /**
   * Retrieve subscription details
   * @param subscriptionId ID of the subscription to retrieve
   * @returns Subscription details
   */
  getSubscription?(subscriptionId: string): Promise<IPaymentResponse>;
}
