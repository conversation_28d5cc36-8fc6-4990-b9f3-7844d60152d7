import { z } from 'zod';

/////////////////////////////////////////
// PASSWORD SCHEMA
/////////////////////////////////////////

export const PasswordSchema = z.object({
  id: z.string().uuid(),
  hash: z.string(),
  salt: z.string(),
  passwordResetToken: z.string().nullable(),
  passwordResetExpiresAt: z.coerce.date().nullable(),
  userId: z.string(),
})

export type Password = z.infer<typeof PasswordSchema>

export default PasswordSchema;
