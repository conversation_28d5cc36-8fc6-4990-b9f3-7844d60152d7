import { Injectable } from '@nestjs/common';
import { C<PERSON>rency, PrismaClient } from '@prisma/client';
import { convertNPRToUSD } from 'src/_utils/currency.utils';

@Injectable()
export class PrismaService extends PrismaClient {
  constructor() {
    super({
      // log: ['query', 'info', 'warn', 'error'],
      log: ['warn', 'error'],
      errorFormat: 'pretty',
    });
  }

  async onModuleInit() {
    await this.$connect();
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }
}
