import { createZodDto } from 'nestjs-zod';
import { UserCreateSchema, UserUpdateSchema, UserSchema, UserResponseSchema, UserResponseWithRoleSchema, ChangePasswordSchema } from './user.schema';
import { z } from 'zod';

export class UserDto extends createZodDto(UserSchema) {}

export class UserCreateDto extends createZodDto(UserCreateSchema) {}

export class UserUpdateDto extends createZodDto(UserUpdateSchema) {}

export class UserResponseDto extends createZodDto(UserResponseSchema) {}

export class UserResponseWithRoleDto extends createZodDto(UserResponseWithRoleSchema) {}

export class ChangePasswordDto extends createZodDto(ChangePasswordSchema) {}
