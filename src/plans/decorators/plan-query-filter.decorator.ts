import { applyDecorators } from '@nestjs/common';
import { ApiQuery } from '@nestjs/swagger';

export function ApiPlanQueryFilter() {
  return applyDecorators(
    ApiQuery({
      name: 'currency',
      required: false,
      description: 'Currency to filter plans by',
      enum: ['USD', 'NPR'],
    }),
    ApiQuery({
      name: 'interval',
      required: false,
      description: 'Billing interval to filter plans by',
      enum: ['MONTHLY', 'YEARLY'],
    }),
    ApiQuery({
      name: 'region',
      required: false,
      description: 'Region to filter plans by',
      enum: ['US', 'NP'],
    }),
  );
}
