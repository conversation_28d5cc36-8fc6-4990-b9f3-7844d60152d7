import { Injectable } from '@nestjs/common';
import { PlansService } from 'src/plans/plans.service';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateSubscriptionDto } from './dto/subscription.dto';

@Injectable()
export class SubscriptionService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly plansService: PlansService,
  ) {}

  async subscribe(CreateSubscriptionDto: CreateSubscriptionDto) {}
}
