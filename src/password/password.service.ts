import { Injectable, Logger, InternalServerErrorException, BadRequestException } from '@nestjs/common';
import { Password, Prisma, PrismaClient } from '@prisma/client';
import { HashService } from 'src/_utils/hash.utils';
import { PrismaService } from 'src/prisma/prisma.service';

/**
 * Service responsible for password management and validation
 * Handles password creation, updates, validation, and reset functionality
 */
@Injectable()
export class PasswordService {
  private readonly logger = new Logger(PasswordService.name);
  private readonly PASSWORD_RESET_EXPIRY_MS = 3600000; // 1 hour in milliseconds

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates or updates a user's password
   * @param userId - ID of the user
   * @param password - Plain text password to hash and store
   * @param prisma - Prisma client instance (for transaction support)
   * @throws InternalServerErrorException on failure
   */
  async createOrUpadatePassword(userId: string, password: string, prisma: PrismaClient | Prisma.TransactionClient = this.prisma): Promise<void> {
    const { hash, salt } = HashService.generateHash(password);

    await prisma.password.upsert({
      create: { userId, hash, salt },
      update: { hash, salt },
      where: { userId },
    });
  }

  /**
   * Generates and stores a password reset token
   * @param email - ID of the user requesting password reset
   * @returns Promise containing the generated token
   * @throws InternalServerErrorException on failure
   */
  async getResetPasswordToken(email: string): Promise<string> {
    const token = HashService.generateToken();
    const expirationDate = new Date(Date.now() + this.PASSWORD_RESET_EXPIRY_MS);
    const user = await this.prisma.user.findUnique({ where: { email } });
    if (!user) throw new BadRequestException('The provided email address is not associated with any account.');
    await this.prisma.password.update({
      where: { userId: user.id },
      data: {
        passwordResetToken: token,
        passwordResetExpiresAt: expirationDate,
      },
    });

    return token;
  }

  /**
   * Resets a user's password using a valid reset token
   * @param userId - ID of the user
   * @param token - Password reset token to validate
   * @param newPassword - New password to set
   * @throws Error if token is invalid or expired
   * @throws InternalServerErrorException on other failures
   */
  async resetPassword(userId: string, token: string, newPassword: string): Promise<void> {
    const isValidToken = await this.validateResetPasswordToken(userId, token);

    if (!isValidToken) {
      throw new InternalServerErrorException('Invalid or expired token');
    }

    await this.createOrUpadatePassword(userId, newPassword);

    // Clear the reset token after successful password change
    await this.clearResetToken(userId);
  }

  /**
   * Validates a user's password
   * @param userId - ID of the user
   * @param password - Password to validate
   * @returns Promise containing boolean indicating if password is valid
   */
  async validateUserPassword(userId: string, password: string): Promise<boolean> {
    const userPassword = await this.findPasswordRecord(userId);

    if (!userPassword) {
      return false;
    }

    return HashService.compareHash({
      tobeHashed: password,
      storedHash: userPassword.hash,
      storedSalt: userPassword.salt,
    });
  }

  /**
   * Helper method to find a password record
   * @param userId - ID of the user
   * @returns Promise containing the password record or null
   */
  async findPasswordRecord(userId: string) {
    return this.prisma.password.findUnique({
      where: { userId },
    });
  }

  /**
   * Clears a user's password reset token
   * @param userId - ID of the user
   * @throws InternalServerErrorException on failure
   */
  private async clearResetToken(userId: string): Promise<void> {
    try {
      await this.prisma.password.update({
        where: { userId },
        data: {
          passwordResetToken: null,
          passwordResetExpiresAt: null,
        },
      });
    } catch (error) {
      this.logger.error(`Failed to clear reset token for user ${userId}`, error);
      throw new InternalServerErrorException('Failed to clear password reset token');
    }
  }

  /**
   * Validates a password reset token
   * @param userId - ID of the user
   * @param token - Token to validate
   * @returns Promise containing boolean indicating if token is valid
   */
  private async validateResetPasswordToken(userId: string, token: string): Promise<boolean> {
    const password = await this.findPasswordRecord(userId);

    if (!password?.passwordResetToken || !password?.passwordResetExpiresAt) {
      return false;
    }

    if (password.passwordResetToken !== token) {
      return false;
    }

    if (password.passwordResetExpiresAt < new Date()) {
      return false;
    }

    return true;
  }
}
