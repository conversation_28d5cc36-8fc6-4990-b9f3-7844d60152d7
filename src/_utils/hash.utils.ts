import { pbkdf2Sync, randomBytes, randomUUID, createHmac } from 'node:crypto';

export class HashService {
  private static HASH_ITERATIONS = 1000;
  private static HASH_LENGTH = 64;
  private static HASH_ALGORITHM = 'sha512';

  static generateHash(password: string): { hash: string; salt: string } {
    //generating the salt of 16 bytes hex
    const salt = randomBytes(16).toString('hex');
    // hashing the password using PBKDF2 algorithm
    const hash = pbkdf2Sync(password, salt, this.HASH_ITERATIONS, this.HASH_LENGTH, this.HASH_ALGORITHM).toString('hex');

    return { hash, salt };
  }
  static compareHash({ tobeHashed, storedHash, storedSalt }: { tobeHashed: string; storedHash: string; storedSalt: string }): boolean {
    const inputHash = pbkdf2Sync(tobeHashed, storedSalt, this.HASH_ITERATIONS, this.HASH_LENGTH, this.HASH_ALGORITHM).toString('hex');
    const passwordsMatch = storedHash === inputHash;
    return passwordsMatch;
  }

  static generateToken(): string {
    return randomUUID();
  }

  static generateEsewaSignature(secretKey: string, message: string): string {
    return createHmac('sha256', secretKey).update(message).digest('base64');
  }
}
