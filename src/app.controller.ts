import { PublicEndpoint } from './auth/decorators/publicEnpoint.decorator';
import { Controller, Get, HttpStatus, Logger } from '@nestjs/common';
import { AppService } from './app.service';

@PublicEndpoint()
@Controller()
export class AppController {
  private readonly logger: Logger;
  constructor(private readonly appService: AppService) {
    this.logger = new Logger(AppController.name);
  }

  // get hello controller
  @Get()
  async getHello() {
    return this.appService.getHello();
  }

  //health check for the server
  @Get('health')
  async healthCheck() {
    return { message: 'Server is up and running', status: HttpStatus.OK };
  }
}
