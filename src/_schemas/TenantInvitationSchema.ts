import { z } from 'zod';

/////////////////////////////////////////
// TENANT INVITATION SCHEMA
/////////////////////////////////////////

export const TenantInvitationSchema = z.object({
  id: z.string().uuid(),
  email: z.string(),
  token: z.string(),
  roleId: z.string(),
  isActive: z.boolean(),
  acceptedAt: z.coerce.date().nullable(),
  expiresAt: z.coerce.date(),
  createdAt: z.coerce.date(),
  tenantId: z.string(),
})

export type TenantInvitation = z.infer<typeof TenantInvitationSchema>

export default TenantInvitationSchema;
