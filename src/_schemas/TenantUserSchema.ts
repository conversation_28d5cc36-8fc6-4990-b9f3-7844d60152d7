import { z } from 'zod';

/////////////////////////////////////////
// TENANT USER SCHEMA
/////////////////////////////////////////

export const TenantUserSchema = z.object({
  id: z.string().uuid(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  userId: z.string(),
  tenantId: z.string(),
  roleId: z.string(),
})

export type TenantUser = z.infer<typeof TenantUserSchema>

export default TenantUserSchema;
