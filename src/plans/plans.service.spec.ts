import { Test, TestingModule } from '@nestjs/testing';
import { PlansService } from './plans.service';
import { PrismaService } from '../prisma/prisma.service';
import { NotFoundException } from '@nestjs/common';
import { BillingInterval, Currency, SubscriptionPlan } from '@prisma/client';
import * as currencyUtils from '../_utils/currency.utils';

// Mock the currency utils
jest.mock('../_utils/currency.utils', () => ({
  convertNPRToUSD: jest.fn(),
}));

describe('PlansService', () => {
  let service: PlansService;
  let prismaService: PrismaService;

  // Mock plan data
  const mockPlans = [
    {
      id: '1',
      name: SubscriptionPlan.BASIC,
      description: 'Basic plan',
      amount: 1500,
      interval: BillingInterval.MONTHLY,
      durationInDays: 30,
      features: { users: 5 },
      isActive: true,
      isPopular: false,
    },
    {
      id: '2',
      name: SubscriptionPlan.PREMIUM,
      description: 'Premium plan',
      amount: 3000,
      interval: BillingInterval.MONTHLY,
      durationInDays: 30,
      features: { users: 10 },
      isActive: true,
      isPopular: true,
    },
  ];

  // Mock for PrismaService
  const mockPrismaService = {
    plan: {
      findMany: jest.fn(),
      findFirstOrThrow: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PlansService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<PlansService>(PlansService);
    prismaService = module.get<PrismaService>(PrismaService);

    // Reset mocks before each test
    jest.clearAllMocks();
    mockPrismaService.plan.findMany.mockReset();
    mockPrismaService.plan.findFirstOrThrow.mockReset();
    (currencyUtils.convertNPRToUSD as jest.Mock).mockReset();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all active plans with NPR currency by default', async () => {
      // Arrange
      mockPrismaService.plan.findMany.mockResolvedValue(mockPlans);

      // Act
      const result = await service.findAll({});

      // Assert
      expect(mockPrismaService.plan.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          interval: undefined,
        },
        omit: {
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          id: 'asc',
        },
      });

      expect(result).toHaveLength(2);
      expect(result[0].currency).toBe(Currency.NPR);
      expect(result[0].amount).toBe(mockPlans[0].amount);
      expect(result[1].currency).toBe(Currency.NPR);
      expect(result[1].amount).toBe(mockPlans[1].amount);
    });

    it('should filter plans by interval', async () => {
      // Arrange
      mockPrismaService.plan.findMany.mockResolvedValue([mockPlans[0]]);

      // Act
      const result = await service.findAll({ interval: BillingInterval.MONTHLY });

      // Assert
      expect(mockPrismaService.plan.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          interval: BillingInterval.MONTHLY,
        },
        omit: {
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          id: 'asc',
        },
      });

      expect(result).toHaveLength(1);
      expect(result[0].interval).toBe(BillingInterval.MONTHLY);
    });

    it('should convert prices to USD and add tax when USD currency is requested', async () => {
      // Arrange
      mockPrismaService.plan.findMany.mockResolvedValue(mockPlans);
      (currencyUtils.convertNPRToUSD as jest.Mock).mockImplementation(amount => amount * 0.0075); // Mock conversion rate

      // Act
      const result = await service.findAll({ currency: Currency.USD });

      // Assert
      expect(mockPrismaService.plan.findMany).toHaveBeenCalled();
      expect(currencyUtils.convertNPRToUSD).toHaveBeenCalledTimes(2);

      // Check first plan
      expect(result[0].currency).toBe(Currency.USD);
      expect(currencyUtils.convertNPRToUSD).toHaveBeenCalledWith(1500);

      // USD amount should be NPR converted + 13% tax
      const usdAmount1 = 1500 * 0.0075; // 11.25
      const taxAmount1 = usdAmount1 * 0.13; // 1.4625
      expect(result[0].originalAmount).toBe(usdAmount1);
      expect(result[0].taxRate).toBe(0.13);
      expect(result[0].taxAmount).toBe(taxAmount1);
      expect(result[0].amount).toBe(usdAmount1 + taxAmount1);

      // Check second plan
      expect(result[1].currency).toBe(Currency.USD);
      const usdAmount2 = 3000 * 0.0075; // 22.5
      const taxAmount2 = usdAmount2 * 0.13; // 2.925
      expect(result[1].originalAmount).toBe(usdAmount2);
      expect(result[1].taxAmount).toBe(taxAmount2);
      expect(result[1].amount).toBe(usdAmount2 + taxAmount2);
    });

    it('should throw NotFoundException when no plans are found', async () => {
      // Arrange
      mockPrismaService.plan.findMany.mockResolvedValue([]);

      // Act & Assert
      await expect(service.findAll({})).rejects.toThrow(NotFoundException);
      expect(mockPrismaService.plan.findMany).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a single plan with NPR currency by default', async () => {
      // Arrange
      mockPrismaService.plan.findFirstOrThrow.mockResolvedValue(mockPlans[0]);

      // Act
      const result = await service.findOne('1');

      // Assert
      expect(mockPrismaService.plan.findFirstOrThrow).toHaveBeenCalledWith({
        where: {
          id: '1',
        },
        omit: {
          createdAt: true,
          updatedAt: true,
        },
      });

      expect(result.id).toBe('1');
      expect(result.currency).toBe(Currency.NPR);
      expect(result.amount).toBe(mockPlans[0].amount);
    });

    it('should convert price to USD and add tax when USD currency is requested', async () => {
      // Arrange
      mockPrismaService.plan.findFirstOrThrow.mockResolvedValue(mockPlans[0]);
      (currencyUtils.convertNPRToUSD as jest.Mock).mockImplementation(amount => amount * 0.0075);

      // Act
      const result = await service.findOne('1', { currency: Currency.USD });

      // Assert
      expect(mockPrismaService.plan.findFirstOrThrow).toHaveBeenCalled();
      expect(currencyUtils.convertNPRToUSD).toHaveBeenCalledWith(1500);

      expect(result.currency).toBe(Currency.USD);
      const usdAmount = 1500 * 0.0075; // 11.25
      const taxAmount = usdAmount * 0.13; // 1.4625
      expect(result.originalAmount).toBe(usdAmount);
      expect(result.taxRate).toBe(0.13);
      expect(result.taxAmount).toBe(taxAmount);
      expect(result.amount).toBe(usdAmount + taxAmount);
    });

    it('should throw NotFoundException when plan is not found', async () => {
      // Arrange
      mockPrismaService.plan.findFirstOrThrow.mockRejectedValue(new Error('Plan not found'));

      // Act & Assert
      await expect(service.findOne('999')).rejects.toThrow(Error);
      expect(mockPrismaService.plan.findFirstOrThrow).toHaveBeenCalled();
    });
  });
});
