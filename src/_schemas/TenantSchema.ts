import { z } from 'zod';

/////////////////////////////////////////
// TENANT SCHEMA
/////////////////////////////////////////

export const TenantSchema = z.object({
  id: z.string().uuid(),
  organizationName: z.string(),
  organizationWebsite: z.string().nullable(),
  roleAtOrganization: z.string(),
  contactNumber: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Tenant = z.infer<typeof TenantSchema>

export default TenantSchema;
