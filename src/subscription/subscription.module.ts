import { Module } from '@nestjs/common';
import { SubscriptionService } from './subscription.service';
import { SubscriptionController } from './subscription.controller';
import { PrismaService } from 'src/prisma/prisma.service';
import { PlansService } from 'src/plans/plans.service';

@Module({
  controllers: [SubscriptionController],
  providers: [SubscriptionService, PrismaService, PlansService],
})
export class SubscriptionModule {}
