services:
  # Main application server
  # Builds the application from the Dockerfile in the current directory
  # Uses the development target for local development environment
  assistant-tech-server:
    container_name: assistant-tech-server
    build:
      context: .
      target: development
    ports:
      - '3000:3000' # Maps container port 3000 to host port 3000
    restart: always # Ensures container restarts automatically if it crashes
    env_file:
      - .env # Loads environment variables from .env
    depends_on:
      - postgres # Ensures postgres starts before the app
      - redis # Ensures redis starts before the app
    volumes:
      - ./:/app # Mounts current directory to /app in container for live code changes
      - /app/node_modules # Prevents overwriting container's node_modules with host's
    user: root
    # The command is now defined in the Dockerfile
    networks:
      - assistant-tech-network

    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/health'] # Checks if app is healthy
      interval: 1m30s # Time between health checks
      timeout: 30s # Time to wait for a response
      retries: 5 # Number of retries before marking unhealthy
      start_period: 30s # Grace period before starting health checks
  # PostgreSQL database service
  # Provides persistent data storage for the application
  postgres:
    container_name: postgres-assistant
    image: postgres:15.3-alpine # Uses lightweight Alpine-based PostgreSQL image
    env_file:
      - .env # Loads database credentials from .env
    ports:
      - '5432:5432' # Maps container port 5432 to host port 5432
    volumes:
      - db-data:/var/lib/postgresql/data # Persists database data across container restarts
    restart: always # Ensures database restarts automatically if it crashes
    networks:
      - assistant-tech-network

  # Redis service
  # Used for caching and message queuing
  redis:
    container_name: redis-assistant
    image: redis:7.0-alpine # Uses lightweight Alpine-based Redis image
    ports:
      - '6379:6379' # Maps container port 6379 to host port 6379
    command: redis-server --requirepass redispassword # Sets Redis password
    volumes:
      - redis-data:/data # Persists Redis data across container restarts
    restart: always # Ensures Redis restarts automatically if it crashes
    networks:
      - assistant-tech-network

  # pgAdmin service
  # Web-based PostgreSQL administration tool
  pg-admin:
    container_name: pgadmin-assistant
    image: dpage/pgadmin4:7.6 # Uses pgAdmin 4 image
    ports:
      - '3000:3000' # Maps container port 3000 to host port 3000
    restart: always # Ensures container restarts automatically if it crashes
    env_file:
      - .env.docker # Loads environment variables from .env
    depends_on:
      - postgres # Ensures postgres starts before the app
      - redis # Ensures redis starts before the app
    volumes:
      - ./:/app # Mounts current directory to /app in container for live code changes
      - /app/node_modules # Prevents overwriting container's node_modules with host's
    user: root
    # The command is now defined in the Dockerfile
    networks:
      - assistant-tech-network

    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/health'] # Checks if app is healthy
      interval: 1m30s # Time between health checks
      timeout: 30s # Time to wait for a response
      retries: 5 # Number of retries before marking unhealthy
      start_period: 30s # Grace period before starting health checks
volumes:
  db-data: # Volume for PostgreSQL data persistence
  redis-data: # Volume for Redis data persistence
  pgadmin-data: # Volume for pgAdmin configuration persistence

networks:
  assistant-tech-network:
    driver: bridge
