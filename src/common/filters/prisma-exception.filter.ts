import { ExceptionFilter, Catch, ArgumentsHost, Logger } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { Response, Request } from 'express';

type PrismaError =
  | Prisma.PrismaClientKnownRequestError
  | Prisma.PrismaClientValidationError
  | Prisma.PrismaClientInitializationError
  | Prisma.PrismaClientUnknownRequestError
  | Prisma.PrismaClientRustPanicError;

interface ErrorResponse {
  statusCode: number;
  message: string;
  error?: string;
}

@Catch(
  Prisma.PrismaClientKnownRequestError,
  Prisma.PrismaClientUnknownRequestError,
  Prisma.PrismaClientValidationError,
  Prisma.PrismaClientInitializationError,
  Prisma.PrismaClientRustPanicError,
)
export class PrismaExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(PrismaExceptionFilter.name);

  // Map Prisma error codes to HTTP status codes and messages
  private errorCodeMap: Record<string, { status: number; message: string }> = {
    P2000: { status: 400, message: 'The provided value is too long' },
    P2001: { status: 400, message: 'Record does not exist' },
    P2002: { status: 409, message: 'Unique constraint violation' },
    P2003: { status: 400, message: 'Foreign key constraint failed' },
    P2004: { status: 400, message: 'Database constraint failed' },
    P2005: { status: 400, message: 'Invalid field value' },
    P2006: { status: 400, message: 'Invalid value provided' },
    P2007: { status: 400, message: 'Data validation error' },
    P2008: { status: 400, message: 'Query parsing failed' },
    P2009: { status: 400, message: 'Query validation failed' },
    P2010: { status: 400, message: 'Raw query failed' },
    P2011: { status: 400, message: 'Null constraint violation' },
    P2012: { status: 400, message: 'Missing required value' },
    P2013: { status: 400, message: 'Missing required argument' },
    P2014: { status: 400, message: 'Relation violation' },
    P2015: { status: 404, message: 'Related record not found' },
    P2016: { status: 400, message: 'Query interpretation error' },
    P2017: { status: 400, message: 'Record relation error' },
    P2018: { status: 400, message: 'Required connected records not found' },
    P2019: { status: 400, message: 'Input error' },
    P2020: { status: 400, message: 'Value out of range' },
    P2021: { status: 400, message: 'Table not found' },
    P2022: { status: 400, message: 'Column not found' },
    P2023: { status: 400, message: 'Inconsistent column data' },
    P2024: { status: 500, message: 'Connection pool timeout' },
    P2025: { status: 404, message: 'Resource not found' },
    P2026: { status: 400, message: 'Database error' },
    P2027: { status: 400, message: 'Multiple errors occurred' },
    P2030: { status: 400, message: 'Full-text search error' },
    P2033: { status: 400, message: 'Transaction error' },
    P2034: { status: 409, message: 'Transaction conflict' },
  };

  catch(exception: PrismaError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // Initialize default error response
    const errorResponse: ErrorResponse = {
      statusCode: 500,
      message: 'Database operation failed',
    };

    // Log the error with context
    this.logger.error(`Prisma error on ${request.method} ${request.url}`, {
      exception: exception instanceof Error ? exception.stack : exception,
      body: request.body,
      params: request.params,
      query: request.query,
    });

    // Handle specific error types
    if (exception instanceof Prisma.PrismaClientKnownRequestError) {
      const errorInfo = this.errorCodeMap[exception.code];

      if (errorInfo) {
        errorResponse.statusCode = errorInfo.status;
        errorResponse.message = errorInfo.message;

        // Add specific field information for certain errors
        if (['P2002', 'P2003', 'P2004'].includes(exception.code) && exception.meta) {
          const fields = exception.meta.target || exception.meta.field_name;
          if (fields) {
            errorResponse.error = `Fields affected: ${JSON.stringify(Array.isArray(fields) ? fields.join(', ') : fields)}`;
          }
        }
      }
    } else if (exception instanceof Prisma.PrismaClientValidationError) {
      errorResponse.statusCode = 400;
      errorResponse.message = 'Invalid database query parameters';
    } else if (exception instanceof Prisma.PrismaClientInitializationError) {
      errorResponse.message = 'Database connection failed';
    } else if (exception instanceof Prisma.PrismaClientRustPanicError) {
      errorResponse.message = 'Critical database error';
    }

    // Send the response
    response.status(errorResponse.statusCode).json(errorResponse);
  }
}
