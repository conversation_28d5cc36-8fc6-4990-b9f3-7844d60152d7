## API Endpoint

- users
  - GET /users
  - GET /users/:id
  - POST /users/sign_up?invitation_token=token
  - PUT /users/:id
  - DELETE /users/:id
- Auth
  - POST /auth/login
  - POST /auth/logout
  - POST /auth/refresh
- Password
  - POST /password/forgot
  - POST /password/reset
- Profile
  - GET /profile
  - PUT /profile
- onboarding
  <!-- - GET /onboarding -->
  - POST /onboarding/welcome
  <!-- - PUT /onboarding -->
- settings
  - GET /settings
  - PUT /settings
- notifications
  - GET /notifications
  - PUT /notifications
- messages
  - GET /messages
  - POST /messages
  - PUT /messages/:id
  - DELETE /messages/:id
- contacts
  - GET /contacts
  - POST /contacts
  - PUT /contacts/:id
  - DELETE /contacts/:id
- integrations
  - GET /integrations
  - POST /integrations
  - PUT /integrations/:id
  - DELETE /integrations/:id
- invitations
  - GET /invitations
  - POST /invitations
  - PUT /invitations/:id
  - DELETE /invitations/:id
