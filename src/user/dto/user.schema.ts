import { z } from 'zod';
import { ISystemUser, IUserCreate, IUserResponse, IUserUpdate } from './user.model';
import { SystemRoles, UserStatus } from '@prisma/client';

export const UserSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(2).max(100),
  email: z.string().email(),
  isVerified: z.boolean(),
  roleId: z.string().uuid(),
  userStatus: z.nativeEnum(UserStatus),
  verificationToken: z.string().uuid().nullable(),
  verificationExpiresAt: z.date().nullable(),
});

export const UserCreateSchema: z.ZodSchema<IUserCreate> = UserSchema.pick({
  name: true,
  email: true,
}).extend({
  password: z.string().min(8).max(50),
});

export const UserUpdateSchema: z.ZodSchema<IUserUpdate> = UserSchema.partial().omit({ id: true });
export const UserResponseSchema = UserSchema.omit({
  verificationToken: true,
  verificationExpiresAt: true,
});
export const UserResponseWithRoleSchema = UserResponseSchema.extend({
  role: z.nativeEnum(SystemRoles),
});
export const ChangePasswordSchema = z.object({
  oldPassword: z.string().min(8).max(50),
  newPassword: z.string().min(8).max(50),
});
