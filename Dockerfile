# This is the combined docker file for the both production and development environment
# Note: Please think twice and even more than twice before making any changes to this file

#---- Base Node Image ----#
FROM node:20-alpine AS base

#--- Set Working Directory ----#
WORKDIR /app

#---- Metadata Labels ----#
LABEL maintainer="Assistant Tech Nepal (Suraj Rasaili BK)"
LABEL description="Nest js backend for the Message Integration Platform(Assistant Tech Nepal) with multi-channel support"
LABEL version="1.0.0"

#---- Install dependencies required for the prisma and health checks ----#
RUN apk add --no-cache curl openssl

#---- Create a non-root user  ----#
RUN addgroup -S appgroup && adduser -S appuser -G appgroup
RUN chown -R appuser:appgroup /app

#---- Dependencies Stage ----#
FROM base AS dependencies

#---- Copy package.json and yarn.lock ----#
COPY package.json yarn.lock ./

#---- Install dependencies including devDependencies ----#
RUN yarn install --frozen-lockfile --production=false

#---- Build Stage ----#
FROM dependencies AS build

#---- Copy source files ----#
COPY --chown=appuser:appgroup . .

#---- Generate Prisma Client ----#
RUN yarn prisma generate

#---- Build the application ----#
RUN yarn build

#---- Development ----#
FROM dependencies AS development

    #---- Copy source files ----#
    COPY --chown=appuser:appgroup . .

    #---- Generate Prisma Client ----#
    RUN yarn prisma generate

    #---- Expose Port ----#
    EXPOSE 3000

    #----switch to non-root user ----#
    USER appuser

    #---- Start the application in development mode ----#
    CMD ["sh", "-c", "npx prisma migrate deploy && npx prisma generate && yarn seed && yarn start:dev"]

# ---- Production ----
FROM base AS production

    #---- Copy package files ----#
    COPY package.json yarn.lock ./

    #---- Install production dependencies only  ----#
    RUN yarn install --frozen-lockfile --production

    #---- Copy built application from build stage ----#
    COPY --from=build --chown=appuser:appgroup /app/dist ./dist
    COPY --from=build --chown=appuser:appgroup /app/node_modules/.prisma ./node_modules/.prisma
    COPY --from=build --chown=appuser:appgroup /app/prisma ./prisma

    #---- Expose port ----#
    EXPOSE 3000

    #---- Add health check  ----#
    HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
      CMD curl -f http://localhost:3000/health || exit 1

    #---- Switch to non-root user ----#
    USER appuser

    #---- Start the application ----#
    CMD ["node", "dist/src/main.js"]


