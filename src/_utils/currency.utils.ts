const NPR_TO_USD = 0.0072;
const USD_TO_NPR = 1 / NPR_TO_USD;
const PAISA = 100;

export const convertNPRToUSD = (amount: number): number => {
  const convertedAmount = amount * NPR_TO_USD;
  return customRoundToQuarter(convertedAmount);
};

const customRoundToQuarter = (value: number): number => {
  const multiplier = 4; // To work with quarters (.25)
  const roundedValue = Math.round(value * multiplier) / multiplier;

  return roundedValue;
};

export const convertUSDtoNPR = (amount: number): number => {
  const convertedAmount = amount * USD_TO_NPR;
  return customRoundToQuarter(convertedAmount);
};

export const parsePaisa = (amount: number): number => {
  return amount * PAISA;
};
