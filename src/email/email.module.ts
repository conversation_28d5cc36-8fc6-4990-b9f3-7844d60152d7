import { Modu<PERSON> } from '@nestjs/common';
import { EmailService } from './email.service';
import { MailerModule } from '@nestjs-modules/mailer';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { mailerConfigFactory } from 'src/common/config/mail.config';
import { BullModule } from '@nestjs/bullmq';
import { EmailProcessor } from './processor/email.processor';

@Module({
  imports: [
    ConfigModule,
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: mailerConfigFactory,
      inject: [ConfigService],
    }),
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        connection: {
          host: configService.getOrThrow<string>('REDIS_HOST_URL'),
          port: configService.getOrThrow<number>('REDIS_PORT'),
          username: configService.getOrThrow<string>('REDIS_USERNAME'),
          password: configService.getOrThrow<string>('REDIS_PASSWORD'),
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue({ name: 'emailQueue' }),
  ],
  providers: [EmailService, EmailProcessor],
  exports: [EmailService],
})
export class NodemailerModule {}
