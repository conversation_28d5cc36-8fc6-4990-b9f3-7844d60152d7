import { z } from 'zod';
import { JsonValueSchema } from './JsonValueSchema';
import { Currency, Prisma, TransactionStatus, TransactionType } from '@prisma/client';

/////////////////////////////////////////
// TRANSACTION SCHEMA
/////////////////////////////////////////

export const TransactionSchema = z.object({
  currency: z.nativeEnum(Currency),
  status: z.nativeEnum(TransactionStatus),
  transactionType: z.nativeEnum(TransactionType),
  id: z.string().uuid(),
  paymentMethodId: z.string().nullable(),
  subscriptionId: z.string().nullable(),
  pricingId: z.string(),
  amount: z.instanceof(Prisma.Decimal, { message: "Field 'amount' must be a Decimal. Location: ['Models', 'Transaction']" }),
  purchaseOrderId: z.string().nullable(),
  purchaseOrderName: z.string().nullable(),
  transactionDetails: JsonValueSchema,
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
});

export type Transaction = z.infer<typeof TransactionSchema>;

export default TransactionSchema;
