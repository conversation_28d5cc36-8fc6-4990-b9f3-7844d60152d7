import { createZodDto } from 'nestjs-zod';

import { z } from 'zod';

export const UserLoginSchema = z.object({
  email: z.string().email().toLowerCase(),
  password: z.string().min(8),
});
export const ForgotPasswordSchema = UserLoginSchema.extend({}).omit({ password: true });

export const ResetPasswordSchema = z.object({
  userId: z.string().uuid(),
  token: z.string().uuid(),
  newPassword: z.string().min(8),
});

export const UserResponseSchema = z
  .object({
    id: z.string().uuid(),
    name: z.string().min(2).max(100),
    password: z.string().min(8),
    email: z.string().email().toLowerCase(),
    userStatus: z.string().default('OFFLINE'),
    roleId: z.string().uuid(),
    accessToken: z.string().jwt(),
    refreshToken: z.string().jwt(),
  })
  .omit({ password: true });

export const resendVerificationEmailSchema = z.object({
  email: z.string().email().toLowerCase(),
});

export class ResendVerificationEmailDto extends createZodDto(resendVerificationEmailSchema) {}

export class UserLoginDto extends createZodDto(UserLoginSchema) {}
export class UserResponseDto extends createZodDto(UserResponseSchema) {}
export class ForgotPasswordDto extends createZodDto(ForgotPasswordSchema) {}
export class ResetPasswordDto extends createZodDto(ResetPasswordSchema) {}

export type IResetPassword = z.infer<typeof ResetPasswordSchema>;
