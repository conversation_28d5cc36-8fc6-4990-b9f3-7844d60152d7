/*
  Warnings:

  - Added the required column `expires_at` to the `access_tokens` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `token` on the `access_tokens` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- AlterTable
ALTER TABLE "access_tokens" ADD COLUMN     "expires_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "is_revoked" BOOLEAN NOT NULL DEFAULT false,
DROP COLUMN "token",
ADD COLUMN     "token" UUID NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "access_tokens_token_key" ON "access_tokens"("token");

-- CreateIndex
CREATE INDEX "access_tokens_user_id_idx" ON "access_tokens"("user_id");
