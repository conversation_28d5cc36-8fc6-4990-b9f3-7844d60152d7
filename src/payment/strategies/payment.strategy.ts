import { StripeGatewayService } from './../gateways/stripe-gateway.service';
import { Injectable, Logger } from '@nestjs/common';
import { KhaltiGatewayService } from '../gateways/khalti-gateway.service';
import { PaymentGateway as PaymentGatewayEnum } from '@prisma/client';
import { EsewaGatewayService } from '../gateways/esewa-gateway.service';

/**
 * Payment Strategy class that implements the Strategy pattern
 * for handling different payment gateways
 */
@Injectable()
export class PaymentStrategy {
  private readonly logger = new Logger(PaymentStrategy.name);

  // Map of payment gateways to their service implementations
  private readonly gatewayMap: Record<PaymentGatewayEnum, any> = {} as Record<PaymentGatewayEnum, any>;

  constructor(
    private readonly khaltiGatewayService: KhaltiGatewayService,
    private readonly esewaGatewayService: EsewaGatewayService,
    private readonly stripeGatewayService: StripeGatewayService,
  ) {
    // Initialize the gateway map
    this.gatewayMap[PaymentGatewayEnum.KHALTI] = this.khaltiGatewayService;
    this.gatewayMap[PaymentGatewayEnum.ESEWA] = this.esewaGatewayService;
    this.gatewayMap[PaymentGatewayEnum.STRIPE] = this.stripeGatewayService;
  }

  getStrategy(gateway: PaymentGatewayEnum): any {
    // Get the gateway service from the map
    const gatewayService = this.gatewayMap[gateway];

    if (!gatewayService) {
      throw new Error(`Unsupported payment gateway: ${gateway}`);
    }

    return gatewayService;
  }
}
