import { StripeVerifyPayloadSchema } from './../dto/payment.dto';
import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';
import { InitiatePaymentDto, IStripeVerifyPayload } from '../dto/payment.dto';
import { PlansService } from 'src/plans/plans.service';
import { BillingInterval } from '@prisma/client';

@Injectable()
export class StripeGatewayService {
  private stripe: Stripe;
  private readonly logger = new Logger(StripeGatewayService.name);

  // Environment variables
  private readonly STRIPE_SECRET_KEY: string;
  private readonly CLIENT_BASE_URL: string;
  private readonly WEBHOOK_SECRET: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly plansService: PlansService,
  ) {
    this.STRIPE_SECRET_KEY = this.configService.getOrThrow<string>('STRIPE_SECRET_KEY');
    this.CLIENT_BASE_URL = this.configService.getOrThrow<string>('CLIENT_BASE_URL');
    this.WEBHOOK_SECRET = this.configService.get<string>('STRIPE_WEBHOOK_SECRET', '');

    this.stripe = new Stripe(this.STRIPE_SECRET_KEY);
  }

  // Initiate payment for subscription
  async initiatePayment(payload: InitiatePaymentDto) {
    try {
      this.logger.log(`Initiating Stripe payment for tenant: ${payload.tenantId}, plan: ${payload.planId}`);

      // Get plan details
      const plan = await this.plansService.findOne(payload.planId, { currency: payload.currency });

      if (!plan || !plan.isActive) {
        throw new NotFoundException('The selected plan is not available right now!');
      }

      // Convert interval to Stripe's interval format
      const interval = this.convertIntervalToStripe(plan.interval);

      // Create or get a product for this plan
      const product = await this.getOrCreateProduct(plan.name, plan.description || '');

      // Create or get a price for this product
      const price = await this.getOrCreatePrice(
        product.id,
        Number(plan.amount) * 100, // Convert to cents
        plan.currency.toLowerCase(),
        interval,
      );

      // Create checkout session for subscription
      const session = await this.stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [
          {
            price: price.id,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: `${this.CLIENT_BASE_URL}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${this.CLIENT_BASE_URL}/payment/cancel`,
        client_reference_id: payload.tenantId,
        metadata: {
          tenantId: payload.tenantId,
          planId: payload.planId,
          isPrimary: payload.isPrimary ? 'true' : 'false',
        },
      });

      this.logger.debug('Stripe session created', { sessionId: session.id });
      return session;
    } catch (error) {
      this.logger.error('Error creating Stripe payment session', error);
      throw error;
    }
  }

  // Verify payment
  async verifyPayment(payload: IStripeVerifyPayload) {
    try {
      const result = StripeVerifyPayloadSchema.safeParse(payload);
      if (!result.success) {
        throw new BadRequestException(result.error.issues[0]?.message || 'Validation failed');
      }

      // Get the session ID from the payload
      const sessionId = payload.session_id;
      const session = await this.stripe.checkout.sessions.retrieve(sessionId);

      return {
        status: session.payment_status,
        sessionId: session.id,
        customerId: session.customer,
        subscriptionId: session.subscription,
        metadata: session.metadata,
      };
    } catch (error) {
      this.logger.error('Error verifying Stripe payment', error);
      throw error;
    }
  }

  // Handle webhook events from Stripe
  async handleWebhook(signature: string, payload: Buffer) {
    try {
      let event: Stripe.Event;

      // Verify webhook signature
      if (this.WEBHOOK_SECRET) {
        event = this.stripe.webhooks.constructEvent(payload, signature, this.WEBHOOK_SECRET);
      } else {
        // For development without signature verification
        event = JSON.parse(payload.toString());
      }

      this.logger.log(`Processing webhook event: ${event.type}`);

      // Handle different event types
      switch (event.type) {
        case 'checkout.session.completed':
          await this.handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session);
          break;

        case 'invoice.paid':
          await this.handleInvoicePaid(event.data.object as Stripe.Invoice);
          break;

        case 'invoice.payment_failed':
          await this.handleInvoicePaymentFailed(event.data.object as Stripe.Invoice);
          break;

        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
          break;

        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
          break;

        default:
          this.logger.log(`Unhandled event type: ${event.type}`);
      }

      return { received: true };
    } catch (error) {
      this.logger.error('Error handling webhook', error);
      throw error;
    }
  }

  // Helper methods for webhook event handling
  private async handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
    this.logger.log('Checkout session completed', { sessionId: session.id });
    // Here we would update our database to record the successful subscription
    // we can access metadata like session.metadata.tenantId, etc.
  }

  private async handleInvoicePaid(invoice: Stripe.Invoice) {
    this.logger.log('Invoice paid', { invoiceId: invoice.id });
    // Update subscription status to active
  }

  private async handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
    this.logger.log('Invoice payment failed', { invoiceId: invoice.id });
    // Handle failed payment - notify user, update status, etc.
  }

  private async handleSubscriptionUpdated(subscription: Stripe.Subscription) {
    this.logger.log('Subscription updated', {
      subscriptionId: subscription.id,
      status: subscription.status,
    });
    // Update subscription details in our database
  }

  private async handleSubscriptionDeleted(subscription: Stripe.Subscription) {
    this.logger.log('Subscription deleted', { subscriptionId: subscription.id });
    // Update subscription status to cancelled in your database
  }

  // Helper methods for product and price management
  private async getOrCreateProduct(name: string, description: string) {
    // First try to find an existing product with this name
    const products = await this.stripe.products.list({
      active: true,
      limit: 100,
    });

    const existingProduct = products.data.find(p => p.name === name);
    if (existingProduct) {
      return existingProduct;
    }

    // Create a new product if none exists
    return await this.stripe.products.create({
      name,
      description,
    });
  }

  private async getOrCreatePrice(
    productId: string,
    unitAmount: number,
    currency: string,
    interval: Stripe.PriceCreateParams.Recurring.Interval,
  ) {
    // Try to find an existing price for this product with the same parameters
    const prices = await this.stripe.prices.list({
      product: productId,
      active: true,
      limit: 100,
    });

    const existingPrice = prices.data.find(
      p => p.unit_amount === unitAmount && p.currency === currency && p.recurring?.interval === interval,
    );

    if (existingPrice) {
      return existingPrice;
    }

    // Create a new price if none exists
    return await this.stripe.prices.create({
      product: productId,
      unit_amount: unitAmount,
      currency,
      recurring: { interval },
    });
  }

  // Convert our billing interval to Stripe's format
  private convertIntervalToStripe(interval: BillingInterval): Stripe.PriceCreateParams.Recurring.Interval {
    switch (interval) {
      case BillingInterval.MONTHLY:
        return 'month';
      case BillingInterval.YEARLY:
        return 'year';
      case BillingInterval.LIFETIME:
        // Stripe doesn't have a lifetime option, so we'll use year as a fallback
        return 'year';
      default:
        return 'month';
    }
  }
}
