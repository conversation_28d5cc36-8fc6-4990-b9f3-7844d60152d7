import { SubscriptionStatus } from '@prisma/client';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const CreateSubscriptionSchema = z.object({
  tenantId: z.string().uuid().describe('Tenant ID for the subscription'),
  planId: z.string().uuid().describe('Plan ID for the subscription'),
  startDate: z.string().datetime().describe('Start date of the subscription'),
});

export class CreateSubscriptionDto extends createZodDto(CreateSubscriptionSchema) {}
