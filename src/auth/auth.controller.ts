import { Body, Controller, Get, HttpCode, HttpStatus, Post, Query, Res, UseGuards, Param, Req } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { UserCreateDto } from 'src/user/dto/user.dto';
import { UserService } from 'src/user/user.service';
import { AuthService } from './auth.service';
import { ForgotPasswordDto, ResendVerificationEmailDto, ResetPasswordDto, UserLoginDto } from './dto/auth.dto';
import { User } from '@prisma/client';
import { PublicEndpoint } from './decorators/publicEnpoint.decorator';
import { JwtService } from '@nestjs/jwt';

function isProduction() {
  return process.env.NODE_ENV === 'production';
}

/**
 * Controller handling authentication-related endpoints
 */
@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  private readonly ACCOUNT_TOKEN_MAX_AGE = 3600 * 1000; // 1 hour
  private readonly REFRESH_TOKEN_MAX_AGE = 3600 * 24 * 7; // 1 week

  constructor(
    private readonly authService: AuthService,
    private readonly jwtService: JwtService,
  ) {}

  // Signup Controller
  @PublicEndpoint()
  @Post('signup')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Sign up a new user' })
  @ApiCreatedResponse({ description: 'The user has been successfully created.' })
  @ApiConflictResponse({ description: 'User with this email already exists' })
  @ApiBadRequestResponse({ description: 'Invalid invitation token' })
  @ApiBadRequestResponse({ description: 'Inivitation Expired' })
  @ApiQuery({ name: 'inivitation_token', required: false, description: 'Invitation token' })
  async signup(
    @Body() payload: UserCreateDto,
    @Query('inivitation_token') invitation_token?: string,
    @Res({ passthrough: true }) response?: Response,
  ): Promise<any> {
    console.log(invitation_token);

    const accountToken = await this.authService.signup(payload, invitation_token);

    // Set cookie with token
    this.setCookie(response, 'account_token', accountToken);

    return { message: 'Signup successful. Please check your email to verify your account.' };
  }

  // Login Controller
  @PublicEndpoint()
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Login a user' })
  @ApiCreatedResponse({ description: 'The user has been successfully logged in.' })
  @ApiConflictResponse({ description: 'Please check your credentials' })
  async login(@Body() userPayload: UserLoginDto, @Res({ passthrough: true }) response?: Response): Promise<{ accessToken: string; message: string }> {
    // Authenticate user via auth service
    const { accessToken, refreshToken } = await this.authService.login(userPayload);
    // Return tokens to user
    this.setCookie(response, 'refresh_token', refreshToken);

    return {
      accessToken,
      message: 'Login successful',
    };
  }

  // Logout Controller
  @Post('/logout')
  @ApiOperation({ summary: 'Logout user' })
  @ApiOkResponse({ description: 'Logout successful' })
  async logout(@Req() req: { user: User }, @Res({ passthrough: true }) response?: Response): Promise<{ message: string }> {
    await this.authService.logout(req.user.id);

    this.clearCookie(response, 'refresh_token');

    return {
      message: 'Logout successful',
    };
  }

  // Refresh Controller
  @PublicEndpoint()
  @Get('/refresh')
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiOkResponse({ description: 'Access token refreshed successfully' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized invalid or expired token' })
  async refreshToken(@Req() req: Request, @Res({ passthrough: true }) response?: Response): Promise<{ accessToken: string; message: string }> {
    // get refresh token from the cookies
    const refreshTokenPayload = req.cookies['refresh_token'] as string;
    // generate and update both refresh token and access token
    const { accessToken, refreshToken } = await this.authService.refreshAccessToken(refreshTokenPayload);

    this.setCookie(response, 'refresh_token', refreshToken);
    return {
      accessToken,
      message: 'Access token refreshed successfully',
    };
  }

  //Verify Email  Controller
  @PublicEndpoint()
  @Get('/verify_email')
  @ApiOperation({ summary: 'Verify email' })
  @ApiOkResponse({ description: 'Email verified successfully' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async verifyEmail(
    @Query('token') token: string,
    @Req() req: Request,
    @Res({ passthrough: true }) response: Response,
  ): Promise<{ accessToken: string; message: string }> {
    const accountToken = req.cookies['account_token'] as string;

    const decoded = this.jwtService.verify(accountToken);
    await this.authService.verifyEmail(token, decoded.sub);
    response.clearCookie('account_token');
    const { accessToken, refreshToken } = await this.authService.generateTokens(decoded.sub, decoded.email);
    this.setCookie(response, 'refresh_token', refreshToken);
    return { accessToken, message: 'Email verified successfully' };
  }

  // Resend Email Verification Controller
  @PublicEndpoint()
  @Post('/resend_verification')
  @ApiOperation({ summary: 'Resend verification email' })
  async resendVerificationEmail(@Body() payload: ResendVerificationEmailDto, @Res({ passthrough: true }) response: Response) {
    const accountToken = await this.authService.resendVerificationEmail(payload.email);
    this.setCookie(response, 'account_token', accountToken);
    return { message: 'Verification email resent successfully' };
  }

  // Request Reset Password Controller
  @PublicEndpoint()
  @Get('/forgot_password')
  @ApiOperation({ summary: 'Request reset password' })
  @ApiQuery({ name: 'email', description: 'user email' })
  @ApiOkResponse({ description: 'Reset link is sent to the provided email address' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async forgotPassword(@Query('email') email: ForgotPasswordDto['email']) {
    return this.authService.forgotPassword({ email });
  }

  @Get('test-token')
  async testToken() {
    return HttpStatus.OK;
  }

  // Reset Password Controller
  @PublicEndpoint()
  @Post('/reset_password/:userId/:token')
  @ApiOperation({ summary: 'Reset password' })
  @ApiParam({ name: 'userId', description: 'user id' })
  @ApiParam({ name: 'token', description: 'reset password token' })
  @ApiCreatedResponse({ description: 'Password reset successfully' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async resetPassword(
    @Param('userId') userId: ResetPasswordDto['userId'],
    @Param('token') token: ResetPasswordDto['token'],
    @Body() payload: Pick<ResetPasswordDto, 'newPassword'>,
  ): Promise<{ message: string }> {
    const data = {
      userId,
      token,
      newPassword: payload.newPassword,
    };
    await this.authService.resetPassword(data);
    return { message: 'Password reset successfully' };
  }

  //a method to set cookies to the client
  private async setCookie(@Res({ passthrough: true }) res: Response | undefined, token_name: string, token: string) {
    res?.cookie(token_name, token, {
      httpOnly: true,
      secure: true,
      maxAge: token_name === 'refresh_token' ? this.REFRESH_TOKEN_MAX_AGE : this.ACCOUNT_TOKEN_MAX_AGE,
      sameSite: isProduction() ? 'strict' : 'none',
      path: '/',
    });
  }

  // a method to clear cookies from client
  private async clearCookie(@Res({ passthrough: true }) res: Response | undefined, token_name: string) {
    res?.clearCookie(token_name, {
      httpOnly: true,
      secure: true,
      sameSite: isProduction() ? 'strict' : 'none',
      path: '/',
    });
  }
}
