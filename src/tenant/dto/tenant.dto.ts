// dto/tenant-dto.ts
import { z } from 'zod';
import { createZodDto } from 'nestjs-zod';

export const CreateTenantSchema = z.object({
  organizationName: z.string(),
  organizationWebsite: z.string().optional(),
  roleAtOrganization: z.string(),
  contactNumber: z.string(),
});

export const UpdateTenantSchema = z.object({
  organizationName: z.string().optional(),
  organizationWebsite: z.string().optional(),
  roleAtOrganization: z.string().optional(),
  contactNumber: z.string().optional(),
});

export const InviteToTenantSchema = z.object({
  email: z.string().email().toLowerCase(),
  userId: z.string().uuid(),
});

export const InviteEmailSchema = z.object({
  email: z.string().email().toLowerCase(),
});

export const AcceptInvitationSchema = z.object({
  userId: z.string().uuid(),
});

export class InviteEmailDto extends createZodDto(InviteEmailSchema) {}

export class CreateTenantDto extends createZodDto(CreateTenantSchema) {}
export class UpdateTenantDto extends createZodDto(UpdateTenantSchema) {}
export class InviteToTenantDto extends createZodDto(InviteToTenantSchema) {}
export class AcceptInvitationDto extends createZodDto(AcceptInvitationSchema) {}
