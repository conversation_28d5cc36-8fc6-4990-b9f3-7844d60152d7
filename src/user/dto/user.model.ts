import { User } from '@prisma/client';

export type ISystemUser = Pick<
  User,
  'id' | 'email' | 'name' | 'roleId' | 'isVerified' | 'verificationExpiresAt' | 'verificationToken' | 'userStatus'
>;
export type IUserCreate = Pick<ISystemUser, 'name' | 'email'> & {
  password: string;
};

export type IUserUpdate = Partial<IUserCreate>;

export type IUserResponse = Omit<ISystemUser, 'verificationToken' | 'verificationExpiresAt'>;
