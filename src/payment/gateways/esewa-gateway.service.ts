import { Response } from 'express';
import axios from 'axios';
import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PlansService } from 'src/plans/plans.service';
import { EsewaVerifyPayloadSchema, IEsewaVerifyPayload, InitiatePaymentDto } from '../dto/payment.dto';
import { randomUUID } from 'crypto';
import { HashService } from 'src/_utils/hash.utils';
import { handleAxiosError } from 'src/_utils/axiosErrorHandler';

@Injectable()
export class EsewaGatewayService {
  constructor(
    private readonly configService: ConfigService,
    private readonly plansService: PlansService,
  ) {}

  //logger for the debugging
  private readonly logger = new Logger(EsewaGatewayService.name);

  //enviroment variables
  private readonly CLIENT_BASE_URL = this.configService.get<string>('CLIENT_BASE_URL');
  private readonly ESEWA_PRIVATE_KEY = this.configService.getOrThrow<string>('ESEWA_PRIVATE_KEY');
  private readonly ESEWA_MERCHANT_CODE = this.configService.getOrThrow<string>('ESEWA_MERCHANT_CODE');
  private readonly ESEWA_BASE_URL = 'https://rc-epay.esewa.com.np/api/epay/';

  async initiatePayment(payload: InitiatePaymentDto) {
    const { isPrimary, planId, tenantId = 'test', currency } = payload;
    const plan = await this.plansService.findOne(planId, { currency: currency });

    if (!plan || !plan.isActive) throw new NotFoundException('The selected plan is not avaiable right now!');

    const amount = plan.amount;
    const transactionUuid = randomUUID();
    // esewa payload
    const esewaPayload = {
      amount: amount,
      tax_amount: '0',
      total_amount: amount,
      transaction_uuid: transactionUuid,
      product_code: this.ESEWA_MERCHANT_CODE,
      product_service_charge: '0',
      product_delivery_charge: '0',
      success_url: 'https://developer.esewa.com.np/success',
      failure_url: 'https://developer.esewa.com.np/failure',
      signed_field_names: 'total_amount,transaction_uuid,product_code',
    };

    // sign a hmac signature
    const signatureString = `total_amount=${amount},transaction_uuid=${transactionUuid},product_code=${this.ESEWA_MERCHANT_CODE}`;
    const signed_field_names = 'total_amount,transaction_uuid,product_code';
    const signature = HashService.generateEsewaSignature(this.ESEWA_PRIVATE_KEY, signatureString);

    const newEsewaPayload = {
      ...esewaPayload,
      signed_field_names,
      signature,
    };
    this.logger.debug('Esewa Payload', newEsewaPayload);
    return newEsewaPayload;
  }

  async verifyPayment(payload: IEsewaVerifyPayload) {
    try {
      const result = EsewaVerifyPayloadSchema.safeParse(payload);
      if (!result.success) {
        throw new BadRequestException(result.error.issues || 'Validation failed');
      }
      console.log('the function is working before the api call');

      const response = await axios.get(
        `${this.ESEWA_BASE_URL}/transaction/status?product_code=${result.data.product_code}&total_amount=${result.data.total_amount}&transaction_uuid=${result.data.transaction_uuid}`,
      );
      console.log('the function is working after the api call');
      console.log(response.data);
      return response.data;
    } catch (error) {
      handleAxiosError(error);
    }
  }
}
