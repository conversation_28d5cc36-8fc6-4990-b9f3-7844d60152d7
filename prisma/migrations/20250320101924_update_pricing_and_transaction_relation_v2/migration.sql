/*
  Warnings:

  - Added the required column `pricing_id` to the `transactions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `purchase_order_id` to the `transactions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `purchase_order_name` to the `transactions` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "transactions" DROP CONSTRAINT "transactions_amount_fkey";

-- DropIndex
DROP INDEX "pricing_amount_key";

-- AlterTable
ALTER TABLE "transactions" ADD COLUMN     "pricing_id" UUID NOT NULL,
ADD COLUMN     "purchase_order_id" TEXT NOT NULL,
ADD COLUMN     "purchase_order_name" TEXT NOT NULL;

-- AddForeignKey
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_pricing_id_fkey" FOREIGN KEY ("pricing_id") REFERENCES "pricing"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
