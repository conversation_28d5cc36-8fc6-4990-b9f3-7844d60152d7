/*
  Warnings:

  - The values [ENTERPRISE] on the enum `SubscriptionPlan` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `details` on the `payment_method` table. All the data in the column will be lost.
  - Added the required column `transaction_details` to the `transactions` table without a default value. This is not possible if the table is not empty.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "SubscriptionPlan_new" AS ENUM ('FREE', 'BASIC', 'PREMIUM', 'PLATINUM');
ALTER TABLE "subscriptions" ALTER COLUMN "plan" DROP DEFAULT;
ALTER TABLE "subscriptions" ALTER COLUMN "plan" TYPE "SubscriptionPlan_new" USING ("plan"::text::"SubscriptionPlan_new");
ALTER TYPE "SubscriptionPlan" RENAME TO "SubscriptionPlan_old";
ALTER TYPE "SubscriptionPlan_new" RENAME TO "SubscriptionPlan";
DROP TYPE "SubscriptionPlan_old";
ALTER TABLE "subscriptions" ALTER COLUMN "plan" SET DEFAULT 'FREE';
COMMIT;

-- AlterTable
ALTER TABLE "payment_method" DROP COLUMN "details";

-- AlterTable
ALTER TABLE "transactions" ADD COLUMN     "transaction_details" JSONB NOT NULL;

-- CreateTable
CREATE TABLE "pricing" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "amount" DECIMAL NOT NULL,
    "currency" "Currency" NOT NULL DEFAULT 'NPR',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "pricing_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "pricing_name_idx" ON "pricing"("name");

-- CreateIndex
CREATE UNIQUE INDEX "pricing_name_key" ON "pricing"("name");
