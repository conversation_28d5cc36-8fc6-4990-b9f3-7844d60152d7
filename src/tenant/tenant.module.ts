import { Modu<PERSON> } from '@nestjs/common';
import { TenantService } from './tenant.service';
import { PrismaModule } from 'src/prisma/prisma.module';
import { TenantController } from './tenant.controller';
import { NodemailerModule } from 'src/email/email.module';

@Module({
  imports: [PrismaModule, NodemailerModule],
  providers: [TenantService],
  exports: [TenantService],
  controllers: [TenantController],
})
export class TenantModule {}
