name: Build and Push Docker Image to Docker Hub

on:
  push:
    branches:
      - main
      - development
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'development'
        type: choice
        options:
          - development
          - production

env:
  IMAGE_NAME: assistanttech/message_integration
  DOCKER_BUILDKIT: 1

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
          # Make sure these secrets are properly configured in your GitHub repository settings

      # Set up dynamic tags based on branch
      - name: Set up Docker tags
        id: docker_tags
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "TAGS=${{ env.IMAGE_NAME }}:latest,${{ env.IMAGE_NAME }}:$(echo ${{ github.sha }} | cut -c1-7)" >> $GITHUB_ENV
          elif [[ "${{ github.ref }}" == "refs/heads/development" ]]; then
            echo "TAGS=${{ env.IMAGE_NAME }}:dev,${{ env.IMAGE_NAME }}:$(echo ${{ github.sha }} | cut -c1-7)" >> $GITHUB_ENV
          else
            echo "TAGS=${{ env.IMAGE_NAME }}:$(echo ${{ github.sha }} | cut -c1-7)" >> $GITHUB_ENV
          fi

      - name: Build and push Docker image
        id: build-and-push
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ env.TAGS }}
          target: development
          cache-from: type=registry,ref=${{ env.IMAGE_NAME }}:buildcache
          cache-to: type=registry,ref=${{ env.IMAGE_NAME }}:buildcache,mode=max
          build-args: |
            NODE_ENV=development

      - name: Image digest
        run: echo ${{ steps.build-and-push.outputs.digest }}
