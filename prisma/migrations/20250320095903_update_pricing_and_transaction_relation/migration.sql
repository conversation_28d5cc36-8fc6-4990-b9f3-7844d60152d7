/*
  Warnings:

  - A unique constraint covering the columns `[amount]` on the table `pricing` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateIndex
CREATE UNIQUE INDEX "pricing_amount_key" ON "pricing"("amount");

-- AddForeignKey
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_amount_fkey" FOREIGN KEY ("amount") REFERENCES "pricing"("amount") ON DELETE RESTRICT ON UPDATE CASCADE;
