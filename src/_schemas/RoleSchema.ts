import { SystemRoles } from '@prisma/client';
import { z } from 'zod';

/////////////////////////////////////////
// ROLE SCHEMA
/////////////////////////////////////////

export const RoleSchema = z.object({
  type: z.nativeEnum(SystemRoles),
  id: z.string().uuid(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
});

export type Role = z.infer<typeof RoleSchema>;

export default RoleSchema;
