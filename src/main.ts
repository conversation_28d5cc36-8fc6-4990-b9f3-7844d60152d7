import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerModule, DocumentBuilder, SwaggerDocumentOptions } from '@nestjs/swagger';
import { patchNestJsSwagger } from 'nestjs-zod';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import * as cookieParser from 'cookie-parser';
import { PrismaExceptionFilter } from './common/filters/prisma-exception.filter';
import { NextFunction, Request, Response } from 'express';
import * as bodyParser from 'body-parser';
async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Patch Swagger to support Zod schemas
  patchNestJsSwagger();

  const configService = app.get(ConfigService);

  // Get environment variables with fallback values
  const PORT = configService.get<number>('PORT') || 3000;
  const API_VERSION = configService.get<string>('API_VERSION') || 'v0.0';

  // Swagger configuration
  const config = new DocumentBuilder()
    .setTitle('Message Integration Platform')
    .setDescription('The Message Integration Platform API description')
    .setVersion(API_VERSION)
    .build();

  // Enable CORS
  app.enableCors({
    origin: ['http://localhost:5173', 'http://localhost'], // Allow requests from this origin
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE', // Allowed HTTP methods
    credentials: true, // Allow credentials (cookies, authorization headers)
    allowedHeaders: ['Content-Type', 'Authorization'], // Allowed headers
  });

  app.use((req: Request, res: Response, next: NextFunction) => {
    if (req.path === '/') {
      return res.redirect(302, '/api/docs');
    }
    next();
  });

  // Set global prefix for all routes
  app.setGlobalPrefix('api');

  // Enable cookie parsing
  app.use(cookieParser());

  // Configure body parser for webhooks
  // Use raw body parser for webhook routes
  app.use('/api/payment/webhook', bodyParser.raw({ type: 'application/json' }));

  // Use regular JSON parser for other routes
  app.use(bodyParser.json({ limit: '10mb' }));
  app.use(bodyParser.urlencoded({ extended: true }));

  //enable global exception filter
  app.useGlobalFilters(new PrismaExceptionFilter());

  // Create Swagger document
  const options: SwaggerDocumentOptions = {
    operationIdFactory: (controllerKey: string, methodKey: string) => methodKey,
  };
  const document = SwaggerModule.createDocument(app, config, options);
  SwaggerModule.setup('api/docs', app, document, {
    customSiteTitle: 'Message Integration Platform Api Docs',
    customfavIcon: 'https://ik.imagekit.io/assistanttech/favicon.png',
    customCss: `
    /* Import Nunito Sans from Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;600;700&display=swap');

    /* Apply Nunito Sans globally to all Swagger UI elements */
    .swagger-ui * {
      font-family: 'Nunito Sans', sans-serif !important;
    }
    .topbar {
    display :none !important;
    }

    `,
  }); // Serve Swagger UI at /api/docs

  // Start the application
  await app.listen(PORT);

  Logger.log(`Application is running on: http://localhost:${PORT}`);
  Logger.log(`Swagger UI is available at: http://localhost:${PORT}/api/docs`);
}

void bootstrap();
