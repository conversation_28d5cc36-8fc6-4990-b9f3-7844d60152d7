import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { SystemRoles } from '@prisma/client';
import { ROLES_KEY } from '../decorators/roles.decorator';
import { UserResponseWithRoleDto } from 'src/user/dto/user.dto';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<SystemRoles>(ROLES_KEY, [context.getClass(), context.getHandler()]);
    
    if (!requiredRoles || requiredRoles.length === 0) return true; // No roles required, allow access
    const { user } = context.switchToHttp().getRequest() as { user: UserResponseWithRoleDto };
    if (!user || !user.role) return false; // User not authenticated or role not found, deny access

    return requiredRoles.includes(user.role);
  }
}
