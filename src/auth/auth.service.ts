import { BadRequestException, Injectable, Logger, NotFoundException, UnauthorizedException } from '@nestjs/common';
import type { ForgotPasswordDto, ResetPasswordDto, UserLoginDto } from './dto/auth.dto';
import { UserService } from 'src/user/user.service';
import { User } from '@prisma/client';
import { PrismaService } from 'src/prisma/prisma.service';
import { PasswordService } from 'src/password/password.service';
import { JwtService } from '@nestjs/jwt';
import { randomUUID } from 'crypto';
import { UserCreateDto, UserResponseWithRoleDto } from 'src/user/dto/user.dto';
import { EmailService } from 'src/email/email.service';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly genericAuthError = 'Invalid credentials';
  private readonly REFRESH_TOKEN_EXPIRATION = 30 * 24 * 3600 * 1000; // 30 days
  private readonly RESET_PASSWORD_EXPIRATION = 15 * 3600 * 1000; // 15 minutes

  constructor(
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
    private readonly prismaService: PrismaService,
    private readonly passwordService: PasswordService,
    private readonly mailService: EmailService,
  ) {}

  /**
   * Authenticates a user with email/password credentials
   * @param userPayload - User login credentials
   * @returns Authenticated user entity
   * @throws BadRequestException for invalid credentials or unverified email
   * @throws InternalServerErrorException for unexpected errors
   */

  async login(userPayload: UserLoginDto): Promise<{ accessToken: string; refreshToken: string }> {
    const user = await this.prismaService.user.findFirst({
      where: { email: userPayload.email },
      include: { tenantUser: true },
    });

    if (!user) {
      throw new BadRequestException(this.genericAuthError);
    }

    if (!user.isVerified) {
      throw new BadRequestException('Please verify your email');
    }

    const isValidPassword = await this.passwordService.validateUserPassword(user.id, userPayload.password);

    if (!isValidPassword) {
      throw new BadRequestException(this.genericAuthError);
    }
    const tokens = await this.generateTokens(user.id, user.email);
    return tokens;
  }

  /**
   * Registers a new user in the system
   * @param payload - User creation data including email, password, and other required fields
   * @param invitation_token - Optional token for users invited to join an existing tenant
   * @returns JWT account token for initial authentication
   * @throws BadRequestException for invalid invitation tokens or expired invitations
   * @throws ConflictException when a user with the provided email already exists
   */
  async signup(payload: UserCreateDto, invitation_token?: string): Promise<string> {
    const user =
      invitation_token !== undefined
        ? await this.userService.createUserWithInvitation(payload, invitation_token)
        : await this.userService.createTenantAdmin(payload);
    const accountToken = this.jwtService.sign({ sub: user.id, email: user.email }, { expiresIn: '1d' }); // generate account token which is valid for the 1d

    // send verification email
    await this.mailService.sendVerificationEmail(user.email, user.name, user.verificationToken);

    return accountToken;
  }

  async logout(userId: string): Promise<void> {
    await this.prismaService.refreshToken.deleteMany({ where: { userId } });
  }

  /**
   * Refreshes the access token using a valid refresh token
   * @param refreshToken - The refresh token from the cookie
   * @returns New access and refresh tokens
   * @throws UnauthorizedException for invalid or expired refresh tokens
   */
  async refreshAccessToken(refreshToken: string): Promise<{ accessToken: string; refreshToken: string }> {
    //find the token record in the database
    if (refreshToken === undefined) {
      throw new BadRequestException('Invalid or expired refresh token');
    }

    const tokenRecord = await this.prismaService.refreshToken.findFirstOrThrow({
      where: { token: refreshToken },
      include: { user: true },
    });
    console.log(tokenRecord);

    // Verify the token exists and is not expired
    if (!tokenRecord || tokenRecord.expiresAt < new Date()) {
      throw new UnauthorizedException('Invalid or expired refresh token');
    }

    const user = tokenRecord.user;

    const tokens = await this.generateTokens(user.id, user.email);

    return tokens;
  }

  async forgotPassword({ email }: ForgotPasswordDto): Promise<string> {
    const user = await this.userService.getUserByEmail(email);
    const passwordResetToken = await this.passwordService.getResetPasswordToken(email);

    //send password reset email
    await this.mailService.sendPasswordResetEmail(user.email, user.name, user.id, passwordResetToken);
    return 'Reset link is sent to the provided email address';
  }

  async resetPassword({ userId, token, newPassword }: ResetPasswordDto): Promise<string> {
    await this.passwordService.resetPassword(userId, token, newPassword);
    const user = await this.userService.getUserById(userId);
    await this.mailService.sendPasswordResetSuccessEmail(user.email, user.name);
    return 'Password reset successfully';
  }

  async verifyEmail(token: string, userId: string): Promise<User> {
    const user = await this.prismaService.user.findUnique({ where: { id: userId } });
    if (!user) throw new NotFoundException('user not found');
    const isVerificationTokenValid = user.verificationToken === token;
    if (!isVerificationTokenValid) throw new BadRequestException('Invalid verification token');

    await this.prismaService.user.update({
      where: {
        id: user.id,
      },
      data: {
        isVerified: true,
        verificationToken: null,
      },
    });
    return user;
  }

  async resendVerificationEmail(email: string) {
    const user = await this.userService.getUserByEmail(email);
    if (!user) throw new NotFoundException('user not found');
    if (user.isVerified) throw new BadRequestException('user already verified');
    const verificationToken = this.userService.generateEmailVerificationToken();
    await this.prismaService.user.update({
      where: {
        id: user.id,
      },
      data: {
        verificationToken,
      },
    });
    const accountToken = this.jwtService.sign({ sub: user.id, email: user.email }, { expiresIn: '1d' }); // generate account token which is valid for the 1d

    // send verification email
    await this.mailService.sendVerificationEmail(user.email, user.name, verificationToken);

    return accountToken;
  }

  async validateUser(email: string, password: string): Promise<UserResponseWithRoleDto> {
    const user = await this.userService.getUserByEmail(email);
    if (!user) throw new BadRequestException('The provided email address is not associated with any account.');

    const isPasswordValid = await this.passwordService.validateUserPassword(user.id, password);
    if (!isPasswordValid) throw new BadRequestException('The provided password is incorrect.');
    return user;
  }

  async generateTokens(userId: string, email: string): Promise<{ accessToken: string; refreshToken: string }> {
    //FIXME: Jwt access token must have the tenant id in the payload
    const payload = { sub: userId, email };
    const accessToken = this.jwtService.sign(payload, { expiresIn: '15m' });
    const refreshToken = randomUUID();
    const refreshTokenExpiresAt = new Date(Date.now() + this.REFRESH_TOKEN_EXPIRATION);

    await this.prismaService.refreshToken.upsert({
      where: {
        userId,
      },
      create: {
        userId,
        token: refreshToken,
        expiresAt: refreshTokenExpiresAt,
        isRevoked: false,
      },
      update: {
        token: refreshToken,
        expiresAt: refreshTokenExpiresAt,
        isRevoked: false,
      },
    });

    return { accessToken, refreshToken };
  }
}
