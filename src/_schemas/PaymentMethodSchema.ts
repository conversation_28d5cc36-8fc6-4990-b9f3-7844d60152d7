import { PaymentGateway } from '@prisma/client';
import { z } from 'zod';

/////////////////////////////////////////
// PAYMENT METHOD SCHEMA
/////////////////////////////////////////

export const PaymentMethodSchema = z.object({
  paymentGateway: z.nativeEnum(PaymentGateway),
  id: z.string().uuid(),
  isPrimary: z.boolean(),
  tenantId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
});

export type PaymentMethod = z.infer<typeof PaymentMethodSchema>;

export default PaymentMethodSchema;
