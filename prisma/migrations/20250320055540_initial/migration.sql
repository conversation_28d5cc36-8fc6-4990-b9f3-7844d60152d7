/*
  Warnings:

  - The values [CANCLED] on the enum `SubscriptionStatus` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `deleted_at` on the `access_tokens` table. All the data in the column will be lost.
  - You are about to drop the column `deleted_at` on the `payment_method` table. All the data in the column will be lost.
  - You are about to drop the column `payment_type` on the `payment_method` table. All the data in the column will be lost.
  - You are about to drop the column `subscription_id` on the `payment_method` table. All the data in the column will be lost.
  - You are about to drop the column `deleted_at` on the `permissions` table. All the data in the column will be lost.
  - You are about to drop the column `deleted_at` on the `role_permissions` table. All the data in the column will be lost.
  - You are about to drop the column `deleted_at` on the `roles` table. All the data in the column will be lost.
  - You are about to drop the column `deleted_at` on the `subscriptions` table. All the data in the column will be lost.
  - You are about to drop the column `deleted_at` on the `tenant_users` table. All the data in the column will be lost.
  - You are about to drop the column `deleted_at` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the column `deleted_at` on the `users` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[tenant_id]` on the table `tenant_users` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[user_id,tenant_id]` on the table `tenant_users` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `is_primary` to the `payment_method` table without a default value. This is not possible if the table is not empty.
  - Added the required column `payment_gateway` to the `payment_method` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `payment_method` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "TransactionStatus" AS ENUM ('PENDING', 'SUCCESS', 'FAILED', 'REFUNDED', 'CANCELLED', 'REVERSED');

-- CreateEnum
CREATE TYPE "Currency" AS ENUM ('USD', 'NPR');

-- CreateEnum
CREATE TYPE "PaymentGateway" AS ENUM ('CREDIT_CARD', 'ESEWA', 'FONE_PAY', 'KHALTI', 'CONNECT_IPS');

-- AlterEnum
BEGIN;
CREATE TYPE "SubscriptionStatus_new" AS ENUM ('ACTIVE', 'EXPIRED', 'CANCELLED', 'TRIAL');
ALTER TABLE "subscriptions" ALTER COLUMN "status" TYPE "SubscriptionStatus_new" USING ("status"::text::"SubscriptionStatus_new");
ALTER TYPE "SubscriptionStatus" RENAME TO "SubscriptionStatus_old";
ALTER TYPE "SubscriptionStatus_new" RENAME TO "SubscriptionStatus";
DROP TYPE "SubscriptionStatus_old";
COMMIT;

-- DropForeignKey
ALTER TABLE "payment_method" DROP CONSTRAINT "payment_method_subscription_id_fkey";

-- DropIndex
DROP INDEX "payment_method_payment_type_idx";

-- DropIndex
DROP INDEX "payment_method_subscription_id_idx";

-- DropIndex
DROP INDEX "payment_method_subscription_id_key";

-- AlterTable
ALTER TABLE "access_tokens" DROP COLUMN "deleted_at";

-- AlterTable
ALTER TABLE "payment_method" DROP COLUMN "deleted_at",
DROP COLUMN "payment_type",
DROP COLUMN "subscription_id",
ADD COLUMN     "is_primary" BOOLEAN NOT NULL,
ADD COLUMN     "payment_gateway" "PaymentGateway" NOT NULL,
ADD COLUMN     "tenant_id" UUID NOT NULL;

-- AlterTable
ALTER TABLE "permissions" DROP COLUMN "deleted_at";

-- AlterTable
ALTER TABLE "role_permissions" DROP COLUMN "deleted_at";

-- AlterTable
ALTER TABLE "roles" DROP COLUMN "deleted_at";

-- AlterTable
ALTER TABLE "subscriptions" DROP COLUMN "deleted_at";

-- AlterTable
ALTER TABLE "tenant_invitations" ADD COLUMN     "accepted_at" TIMESTAMP(3),
ADD COLUMN     "is_active" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "tenant_users" DROP COLUMN "deleted_at";

-- AlterTable
ALTER TABLE "tenants" DROP COLUMN "deleted_at";

-- AlterTable
ALTER TABLE "users" DROP COLUMN "deleted_at",
ADD COLUMN     "verification_expires_at" TIMESTAMP(3);

-- DropEnum
DROP TYPE "PaymentType";

-- CreateTable
CREATE TABLE "transactions" (
    "id" UUID NOT NULL,
    "payment_method_id" UUID NOT NULL,
    "subscription_id" UUID NOT NULL,
    "amount" DECIMAL NOT NULL,
    "currency" "Currency" NOT NULL DEFAULT 'NPR',
    "status" "TransactionStatus" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "transactions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "transactions_payment_method_id_idx" ON "transactions"("payment_method_id");

-- CreateIndex
CREATE INDEX "transactions_subscription_id_idx" ON "transactions"("subscription_id");

-- CreateIndex
CREATE INDEX "transactions_status_idx" ON "transactions"("status");

-- CreateIndex
CREATE INDEX "payment_method_payment_gateway_idx" ON "payment_method"("payment_gateway");

-- CreateIndex
CREATE UNIQUE INDEX "tenant_users_tenant_id_key" ON "tenant_users"("tenant_id");

-- CreateIndex
CREATE INDEX "tenant_users_user_id_idx" ON "tenant_users"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "tenant_users_user_id_tenant_id_key" ON "tenant_users"("user_id", "tenant_id");

-- AddForeignKey
ALTER TABLE "payment_method" ADD CONSTRAINT "payment_method_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_payment_method_id_fkey" FOREIGN KEY ("payment_method_id") REFERENCES "payment_method"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_subscription_id_fkey" FOREIGN KEY ("subscription_id") REFERENCES "subscriptions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
