import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common';
import { PlansService } from './plans.service';
import { PublicEndpoint } from '../auth/decorators/publicEnpoint.decorator';
import { CreatePlanDto, ProcessedPlanDto, ProcessedPlansDto, PlansFilterDto } from './dto/plans.dto';
import { Roles } from '../auth/decorators/roles.decorator';
import { ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';
import { ApiPlanQueryFilter } from './decorators/plan-query-filter.decorator';

@Controller('plans')
export class PlansController {
  constructor(private readonly plansService: PlansService) {}

  @PublicEndpoint()
  @ApiOperation({ summary: 'Get all available plans' })
  @ApiResponse({ status: 200, description: 'Returns all available plans', type: ProcessedPlansDto })
  @ApiPlanQueryFilter()
  @Get()
  async getPlans(@Query() filter: PlansFilterDto): Promise<ProcessedPlansDto> {
    return await this.plansService.findAll(filter);
  }

  @PublicEndpoint()
  @ApiOperation({ summary: 'Get a specific plan by ID' })
  @ApiResponse({ status: 200, type: ProcessedPlanDto })
  @ApiPlanQueryFilter()
  @ApiParam({ name: 'id', required: true, description: 'ID of the plan to retrieve' })
  @Get(':id')
  async getPlan(@Param() param: { id: string }, @Query() filter: PlansFilterDto): Promise<ProcessedPlanDto> {
    return await this.plansService.findOne(param.id, filter);
  }

  // these endpoints are for the super admin for creating, updating and deleting plans
  // create a new plan
  @Post()
  @Roles('SUPER_ADMIN')
  async createPlan(@Body() payload: CreatePlanDto) {}

  // update the existing plan
  @Put()
  @Roles('SUPER_ADMIN')
  async updatePlan() {}

  // delete the existing plan
  @Delete()
  @Roles('SUPER_ADMIN')
  async deletePlan() {}
}
