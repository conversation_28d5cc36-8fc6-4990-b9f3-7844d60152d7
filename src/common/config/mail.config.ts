import { ConfigService } from '@nestjs/config';
import { EjsAdapter } from '@nestjs-modules/mailer/dist/adapters/ejs.adapter';
import * as path from 'path';

export const mailerConfigFactory = (configService: ConfigService) => {
  //Email Template Directory
  const templateDir = path.join(__dirname, '../../../templates');

  //options for the mailer useFactory method
  return {
    transport: {
      host: configService.getOrThrow<string>('MAIL_HOST'),
      port: configService.getOrThrow<number>('MAIL_PORT'),
      auth: {
        user: configService.getOrThrow<string>('MAIL_USER'),
        pass: configService.getOrThrow<string>('MAIL_PASSWORD'),
      },
    },
    defaults: {
      //change to the official company mailing service
      from: 'Suraj Rasaili <<EMAIL>>  ',
    },
    template: {
      dir: templateDir,
      adapter: new EjsAdapter(),
      options: {
        strict: false,
      },
    },
  };
};
