  <p align="center">
    <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="120" alt="Nest Logo" /></a>
  </p>

    <p align="center">A progressive <a href="http://nodejs.org" target="_blank">Node.js</a> framework for building efficient and scalable server-side applications.</p>
      <p align="center">

<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/v/@nestjs/core.svg" alt="NPM Version" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/l/@nestjs/core.svg" alt="Package License" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/dm/@nestjs/common.svg" alt="NPM Downloads" /></a>
<a href="https://circleci.com/gh/nestjs/nest" target="_blank"><img src="https://img.shields.io/circleci/build/github/nestjs/nest/master" alt="CircleCI" /></a>
<a href="https://coveralls.io/github/nestjs/nest?branch=master" target="_blank"><img src="https://coveralls.io/repos/github/nestjs/nest/badge.svg?branch=master#9" alt="Coverage" /></a>
<a href="https://discord.gg/G7Qnnhy" target="_blank"><img src="https://img.shields.io/badge/discord-online-brightgreen.svg" alt="Discord"/></a>
<a href="https://opencollective.com/nest#backer" target="_blank"><img src="https://opencollective.com/nest/backers/badge.svg" alt="Backers on Open Collective" /></a>
<a href="https://opencollective.com/nest#sponsor" target="_blank"><img src="https://opencollective.com/nest/sponsors/badge.svg" alt="Sponsors on Open Collective" /></a>
<a href="https://paypal.me/kamilmysliwiec" target="_blank"><img src="https://img.shields.io/badge/Donate-PayPal-ff3f59.svg" alt="Donate us"/></a>
<a href="https://opencollective.com/nest#sponsor"  target="_blank"><img src="https://img.shields.io/badge/Support%20us-Open%20Collective-41B883.svg" alt="Support us"></a>
<a href="https://twitter.com/nestframework" target="_blank"><img src="https://img.shields.io/twitter/follow/nestframework.svg?style=social&label=Follow" alt="Follow us on Twitter"></a>

  </p>

## Description

This project is a backend API built using the [NestJS](https://nestjs.com) framework. It provides a robust and scalable server-side application structure, leveraging TypeScript for type safety and maintainability.

## Project Setup

### Prerequisites

Ensure you have the following installed:

- Node.js (v14 or higher)
- npm (v6 or higher)

### Installation

Clone the repository and install the dependencies:

```bash
$ git clone https://github.com/your-repo/backend.git
$ cd backend
$ npm install
```

## Running the Application

### Development

Start the application in development mode:

```bash
$ npm run start:dev
```

### Production

Compile and run the application in production mode:

```bash
$ npm run build
$ npm run start:prod
```

## Testing

### Unit Tests

Run the unit tests:

```bash
$ npm run test
```

### End-to-End Tests

Run the end-to-end tests:

```bash
$ npm run test:e2e
```

### Test Coverage

Generate test coverage reports:

```bash
$ npm run test:cov
```

## Deployment

### CI/CD Pipeline

This project uses GitHub Actions for continuous integration and deployment:

1. **Build and Push Docker Image**

   - Triggered on pushes to `main` and `development` branches
   - Builds the Docker image using the production target
   - Tags the image with:
     - `latest` for the main branch
     - `dev` for the development branch
     - Short SHA of the commit for traceability
   - Pushes the image to Docker Hub

2. **Deploy to EC2**
   - Automatically triggered after successful Docker image build
   - Pulls the latest image from Docker Hub
   - Deploys the application to EC2 instance
   - Sets up health checks and container monitoring
   - Can also be manually triggered with environment selection

### Manual Deployment

For manual deployment, follow the [NestJS deployment documentation](https://docs.nestjs.com/deployment).

## Docker

### Local Development with Docker

```bash
# Start the development environment
$ docker compose up

# Start in detached mode
$ docker compose up -d
```

### Production Deployment with Docker

```bash
# Build and start the production environment
$ docker compose -f docker-compose.production.yml up -d
```

## API Documentation

The API documentation is generated using Swagger. After starting the application, you can access the documentation at:

```
http://localhost:3000/api/docs
```

## Database Seeding

This project uses Prisma for database management and includes a seed script to populate the database with initial data.

### What the Seed Script Does

The seed script (`prisma/seed.ts`) initializes the database with:

1. **System Roles**: Creates default roles (SUPER_ADMIN, TENANT_ADMIN, MEMBER, GUEST)
2. **Permissions**: Sets up system permissions (MANAGE_TENANT, MANAGE_USERS, etc.)
3. **Role Permissions**: Assigns appropriate permissions to each role
4. **Pricing Plans**: Creates subscription plans (FREE, BASIC, PREMIUM, PLATINUM) with:
   - Plan details and features
   - Pricing for different currencies (NPR, USD)
   - Monthly and yearly billing intervals (with 10% discount for yearly)

### Running the Seed Script

You can run the seed script manually with:

```bash
$ npm run seed
# or
$ yarn seed
```

The seed script is automatically executed when:

- Running the application in Docker development environment
- During the Docker build process for development

### When to Use Seeding

Seeding is useful in the following scenarios:

- Initial application setup
- After resetting or migrating the database
- Setting up development or testing environments
- Ensuring consistent data across different environments

## License

## Support

## Stay in Touch

## Acknowledgements

Special thanks to all the contributors and sponsors who make this project possible.
