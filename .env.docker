NODE_ENV=development
API_VERSION=v1.0
PORT=3000
CLIENT_BASE_URL='http://localhost:5173'

# PostgreSQL Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=assistant_tech_db
DATABASE_URL=******************************************************/assistant_tech_db?schema=public

# Redis Configuration
REDIS_HOST_URL=redis-assistant
REDIS_PORT=6379
REDIS_USERNAME=default
REDIS_PASSWORD=redispassword

# pgAdmin Configuration
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=adminpassword

# JWT Configuration
JWT_SECRET=da8ba17c616888af4792f50af0e56dc98dec070e5d115468b6c2e8bfe8bb9004

# Mail Configuration
MAIL_HOST=live.smtp.mailtrap.io
MAIL_PORT=587
MAIL_SECURE=false
MAIL_USER=<EMAIL>
MAIL_PASSWORD=e2481975ceca49e03335feeb5c83d974

# Khalti Configuration
KHALTI_PRIVATE_KEY=d6078d1f18fa4d4db63299efd36b2dd9
KHALTI_PUBLIC_KEY=20d7718514b04d94a0394ea70ebb1c76
KHALTI_PROD_URL=https://khalti.com/api/v2/
KHALTI_TEST_URL=https://dev.khalti.com/api/v2/

# Esewa Configuration
ESEWA_PRIVATE_KEY=8gBm/:&EnhH.1/q
ESEWA_MERCHANT_CODE="EPAYTEST"
ESEWA_TEST_URL=https://rc-epay.esewa.com.np/api/epay/main/v2/form
ESEWA_PROD_URL=https://epay.esewa.com.np/api/epay/main/v2/form

# Google Configuration
GOOGLE_CLIENT_ID=914620070174-sjvk4kn3ajq1gsnj5gn9sl5qn62en6eq.apps.googleusercontent.com

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_51RItyVPs89y1XYqhyEFHe8sVRno0O8QxY5ufOef2AFMOwapZd4kJYa3XeB3e6QXVWuFhEoxoWVP4khz7gKz02jKr00JlnLeoUr
STRIPE_PUBLIC_KEY=pk_test_51RItyVPs89y1XYqhZIojpdrZqh5W9xRr7s2t9ysgeK4gbLGyoD69YQgDRYBhznZCwMgKYtvJHtaJZVGIcprCb2Gl00TVOTXXtm
