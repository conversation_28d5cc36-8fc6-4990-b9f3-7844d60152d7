{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "generate": "npx prisma generate", "start:dev": "nest start --watch", "seed": "ts-node prisma/seed.ts", "debug": "nest start --debug --watch", "prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^11.0.3", "@prisma/client": "^6.5.0", "axios": "^1.8.3", "bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "dotenv": "^16.5.0", "ejs": "^3.1.10", "handlebars": "^4.7.8", "jest": "^29.7.0", "mjml": "^4.15.3", "nestjs-zod": "^4.3.1", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "postmark": "^4.0.5", "prisma": "^6.5.0", "pug": "^3.0.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "stripe": "^18.1.0", "webpack": "^5.98.0", "zod": "^3.24.2", "zod-prisma-types": "^3.2.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@nestjs/bullmq": "^11.0.2", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/ejs": "^3.1.5", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/mjml": "^4.7.4", "@types/node": "^22.10.7", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/pug": "^2.0.10", "@types/supertest": "^6.0.2", "@typescript-eslint/parser": "^8.26.0", "bullmq": "^5.44.3", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "liquidjs": "^10.21.0", "prettier": "^3.4.2", "preview-email": "^3.1.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.2", "typescript-eslint": "^8.26.0"}, "prisma": {"schema": "./prisma"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}