import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { MailerService } from '@nestjs-modules/mailer';
import { IEmailJob } from '../types/email.types';

@Processor('emailQueue')
@Injectable()
export class EmailProcessor extends WorkerHost {
  private readonly logger = new Logger(EmailProcessor.name);

  constructor(private readonly mailerService: MailerService) {
    super();
  }

  async process(job: Job<IEmailJob>): Promise<{ success: boolean }> {
    const { to, subject, template, context } = job.data;

    this.logger.debug(`Processing email job ${job.id}: sending ${subject} email to ${to}`);

    try {
      await this.mailerService.sendMail({
        to,
        subject,
        template,
        context,
      });

      this.logger.log(`Successfully sent ${subject} email to ${to}`);
      return { success: true };
    } catch (error: unknown) {
      const err = error as Error;
      this.logger.error(`Failed to send ${subject} email to ${to}`, err.stack);
      throw error; // Throwing will trigger the job retry mechanism
    }
  }
}
