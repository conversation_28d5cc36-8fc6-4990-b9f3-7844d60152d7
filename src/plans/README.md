# Plans Module

This module handles subscription plans for the application, including:
- Retrieving plans with filtering options
- Converting prices between currencies (NPR to USD)
- Adding tax to USD prices

## Features

- **Currency Conversion**: Automatically converts NPR prices to USD when requested
- **Tax Calculation**: Adds 13% tax to USD prices
- **Filtering**: Filter plans by currency and interval

## API Endpoints

### GET /api/plans
Retrieves all available plans with optional filtering.

**Query Parameters**:
- `currency`: Filter by currency (NPR or USD)
- `interval`: Filter by billing interval (MONTHLY, YEARLY, etc.)

**Response**:
```json
[
  {
    "id": "1",
    "name": "BASIC",
    "description": "Basic plan",
    "amount": 1500,
    "interval": "MONTHLY",
    "durationInDays": 30,
    "features": { "users": 5 },
    "isActive": true,
    "isPopular": false,
    "currency": "NPR"
  },
  {
    "id": "2",
    "name": "PREMIUM",
    "description": "Premium plan",
    "amount": 3000,
    "interval": "MONTHLY",
    "durationInDays": 30,
    "features": { "users": 10 },
    "isActive": true,
    "isPopular": true,
    "currency": "NPR"
  }
]
```

When requesting USD currency, additional tax information is included:
```json
[
  {
    "id": "1",
    "name": "BASIC",
    "description": "Basic plan",
    "amount": 12.75,
    "interval": "MONTHLY",
    "durationInDays": 30,
    "features": { "users": 5 },
    "isActive": true,
    "isPopular": false,
    "currency": "USD",
    "taxRate": 0.13,
    "taxAmount": 1.47,
    "originalAmount": 11.28
  }
]
```

### GET /api/plans/:id
Retrieves a specific plan by ID with optional currency conversion.

**Query Parameters**:
- `currency`: Currency to display (NPR or USD)

## Testing

The plans module has comprehensive tests covering all functionality and edge cases.

### Running Tests

To run all tests for the plans module:
```bash
npm run test:plans
```

To run tests with coverage:
```bash
npm run test:plans:cov
```

### Coverage Thresholds

The coverage configuration enforces the following thresholds:
- Statements: 80%
- Branches: 80%
- Functions: 80%
- Lines: 80%

### Test Files

- `plans.service.spec.ts`: Tests for the plans service
- `plans.controller.spec.ts`: Tests for the plans controller

## Implementation Details

### Currency Conversion

NPR to USD conversion is handled by the `convertNPRToUSD` utility function.

### Tax Calculation

A 13% tax rate is applied to USD prices. The tax information is included in the response:
- `originalAmount`: The USD amount before tax
- `taxRate`: The tax rate (0.13)
- `taxAmount`: The amount of tax added
- `amount`: The total amount including tax
