import { Request } from 'express';
import { Body, Controller, Headers, Post, Req, Logger, HttpStatus, HttpCode, BadRequestException } from '@nestjs/common';
import {
  EsewaPaymentDto,
  IEsewaVerifyPayload,
  InitiatePaymentDto,
  KhaltiPaymentDto,
  StripePaymentDto,
  VerifyPaymentDto,
} from './dto/payment.dto';
import { PaymentStrategy } from './strategies/payment.strategy';
import { PaymentGateway as PaymentGatewayEnum } from '@prisma/client';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { PublicEndpoint } from 'src/auth/decorators/publicEnpoint.decorator';
import { decode } from 'node:punycode';

@Controller('payment')
@PublicEndpoint()
// @Roles('TENANT_ADMIN')
export class PaymentController {
  private readonly logger = new Logger(PaymentController.name);

  constructor(private readonly paymentStrategy: PaymentStrategy) {}

  /**
   * Initiate a payment transaction
   */
  @Post('initiate-payment')
  @ApiOperation({ summary: 'Initiate a payment transaction' })
  @ApiResponse({
    status: 200,
    description: 'Payment initiated successfully. Returns redirect URL or session data.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request. Invalid payment gateway or missing required fields.',
  })
  async initiatePayment(@Body() payload: InitiatePaymentDto) {
    try {
      this.logger.log(`Initiating payment for gateway: ${payload.paymentGateway}`);
      const gateway = this.paymentStrategy.getStrategy(payload.paymentGateway);
      const response = await gateway.initiatePayment(payload);

      return {
        success: true,
        data: response,
        message: 'Payment initiated successfully',
      };
    } catch (error) {
      this.logger.error('Error initiating payment', error);
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Verify a payment transaction
   */
  @Post('verify-payment')
  @ApiOperation({ summary: 'Verify a payment transaction' })
  @ApiResponse({
    status: 200,
    description: 'Payment verification result',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request. Invalid transaction ID or payment gateway.',
  })
  async verifyPayment(@Body() payload: VerifyPaymentDto) {
    try {
      const paymentGateway = payload.paymentGateway.toUpperCase() as PaymentGatewayEnum;

      const gateway = this.paymentStrategy.getStrategy(paymentGateway);

      let verificationResult: any;

      //FIXME: refactor this code using the map based architecture for better readability
      // Handle different payment gateway DTOs
      switch (paymentGateway) {
        case PaymentGatewayEnum.KHALTI:
          // Check if pidx is provided for Khalti payments
          if (!('pidx' in payload) || !(payload as KhaltiPaymentDto).pidx) {
            throw new BadRequestException('Must provide pidx for Khalti payment verification');
          }
          // For Khalti payments

          verificationResult = await gateway.verifyPayment({
            pidx: (payload as KhaltiPaymentDto).pidx,
          });
          break;
        case PaymentGatewayEnum.ESEWA:
          // Check if data is provided for esewa payments
          if (!('data' in payload) || !(payload as EsewaPaymentDto).data) {
            throw new BadRequestException('Must provide data for esewa payment verification');
          }
          // For Esewa payments

          const decoded = Buffer.from((payload as EsewaPaymentDto).data, 'base64').toString();

          const decodedJson = JSON.parse(decoded);
          // The data property contains base64 encoded verification data
          verificationResult = await gateway.verifyPayment(decodedJson as IEsewaVerifyPayload);
          break;
        case PaymentGatewayEnum.STRIPE:
          // Check if data is provided for esewa payments
          if (!('session_id' in payload) || !(payload as StripePaymentDto).session_id) {
            throw new BadRequestException('Must provide session_id for stripe payment verification');
          }
          // For Stripe payments
          verificationResult = await gateway.verifyPayment({
            session_id: (payload as StripePaymentDto).session_id,
          });
          break;
        default:
          const paymentGateway = (payload as any).paymentGateway;
          throw new Error(`Unsupported payment gateway: ${paymentGateway}`);
      }

      return {
        success: true,
        data: verificationResult,
        message: 'Payment verification completed',
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Handle Stripe webhook events
   */
  @Post('webhook/stripe')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Handle Stripe webhook events' })
  @ApiResponse({
    status: 200,
    description: 'Webhook processed successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request. Invalid signature or payload.',
  })
  async handleStripeWebhook(@Headers('stripe-signature') signature: string, @Req() request: Request) {
    try {
      if (!signature) {
        this.logger.warn('Missing Stripe signature');
        return {
          success: false,
          error: 'Missing stripe-signature header',
          message: 'Webhook signature verification failed',
        };
      }

      // Get raw body from request
      const payload = request.body;

      // Convert body to buffer if it's not already
      const rawBody = Buffer.isBuffer(payload) ? payload : Buffer.from(JSON.stringify(payload));

      // Get Stripe gateway
      const stripeGateway = this.paymentStrategy.getStrategy(PaymentGatewayEnum.STRIPE);

      // Process webhook - ensure handleWebhook exists
      if (typeof stripeGateway.handleWebhook === 'function') {
        const result = await stripeGateway.handleWebhook(signature, rawBody);
        return {
          success: true,
          data: result,
          message: 'Webhook processed successfully',
        };
      } else {
        throw new Error('Webhook handler not implemented for this gateway');
      }
    } catch (error) {
      this.logger.error('Error processing Stripe webhook', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to process webhook',
      };
    }
  }
}
