import { TransactionStatus } from '@prisma/client';
import { z } from 'zod';

/////////////////////////////////////////
// SUBSCRIPTION SCHEMA
/////////////////////////////////////////

export const SubscriptionSchema = z.object({
  status: z.nativeEnum(TransactionStatus),
  id: z.string().uuid(),
  tenantId: z.string(),
  pricingId: z.string(),
  startDate: z.coerce.date(),
  endDate: z.coerce.date(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
});

export type Subscription = z.infer<typeof SubscriptionSchema>;

export default SubscriptionSchema;
